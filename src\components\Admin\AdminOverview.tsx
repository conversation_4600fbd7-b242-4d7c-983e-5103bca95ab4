import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  Chip,
  LinearProgress,
  Button
} from '@mui/material';
import {
  People as PeopleIcon,
  Storage as StorageIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Assessment as AssessmentIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';
import { getEnhancedUserStatistics } from '../../utils/adminStats';

interface SystemStats {
  total_users: number;
  total_profiles: number;
  admin_users: number;
  standard_users: number;
  pro_users: number;
  edu_users: number;
  edu_pro_users: number;
  users_with_datasets: number;
  total_datasets: number;
  users_last_7_days: number;
  users_last_30_days: number;
  active_users_last_7_days: number;
  active_users_last_30_days: number;
}

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
  trend?: {
    value: number;
    label: string;
    positive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, subtitle, trend }) => {
  const theme = useTheme();
  
  return (
    <Card 
      elevation={0} 
      variant="outlined"
      sx={{ 
        height: '100%',
        borderRadius: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4]
        }
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ 
            p: 1.5, 
            borderRadius: 2, 
            backgroundColor: alpha(color, 0.1),
            color: color
          }}>
            {icon}
          </Box>
          {trend && (
            <Chip
              size="small"
              label={trend.label}
              color={trend.positive ? 'success' : 'error'}
              variant="outlined"
            />
          )}
        </Box>
        
        <Typography variant="h4" component="div" fontWeight="bold" color={color} gutterBottom>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </Typography>
        
        <Typography variant="h6" color="text.primary" gutterBottom>
          {title}
        </Typography>
        
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

const AdminOverview: React.FC = () => {
  const theme = useTheme();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSystemStats();
  }, []);

  const fetchSystemStats = async (retryCount = 0) => {
    try {
      if (retryCount === 0) {
        setLoading(true);
      }
      setError(null);

      // Use enhanced statistics function with fallback calculations
      console.log('🔄 Fetching enhanced user statistics...');
      const data = await getEnhancedUserStatistics();

      setStats(data);
      console.log('✅ System statistics loaded successfully');
      console.log('📊 Statistics data:', data);

      // Log specific problematic values
      console.log('🔍 Active users last 7 days:', data.active_users_last_7_days);
      console.log('🔍 Users last 7 days:', data.users_last_7_days);
      console.log('🔍 Users last 30 days:', data.users_last_30_days);
    } catch (err: any) {
      console.error('Error fetching system statistics:', err);

      // Retry logic for connection issues
      if (retryCount < 2 && (err.message?.includes('network') || err.message?.includes('connection'))) {
        console.log(`🔄 Retrying system statistics fetch (attempt ${retryCount + 1})`);
        setTimeout(() => fetchSystemStats(retryCount + 1), 1000 * (retryCount + 1));
        return;
      }

      setError(err.message || 'Failed to load system statistics. Please try refreshing the page.');
    } finally {
      if (retryCount === 0) {
        setLoading(false);
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300 }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading system overview...
          </Typography>
        </Box>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Error Loading System Statistics
        </Typography>
        <Typography>
          {error}
        </Typography>
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning">
        <Typography>
          No system statistics available.
        </Typography>
      </Alert>
    );
  }

  const userGrowthRate = stats.users_last_30_days > 0 
    ? ((stats.users_last_7_days / stats.users_last_30_days) * 100).toFixed(1)
    : '0';

  const activeUserRate = stats.total_users > 0
    ? ((stats.active_users_last_7_days / stats.total_users) * 100).toFixed(1)
    : '0';

  const datasetUsageRate = stats.total_users > 0
    ? ((stats.users_with_datasets / stats.total_users) * 100).toFixed(1)
    : '0';

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{
        mb: 4,
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: 2
      }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}>
            System Overview
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
            Real-time statistics and key metrics for DataStatPro
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => fetchSystemStats()}
          disabled={loading}
          size="small"
        >
          Refresh
        </Button>
      </Box>

      {/* Key Metrics Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Total Users"
            value={stats.total_users}
            icon={<PeopleIcon />}
            color={theme.palette.primary.main}
            subtitle="Registered accounts"
            trend={{
              value: stats.users_last_7_days,
              label: `+${stats.users_last_7_days} this week`,
              positive: true
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Active Users"
            value={stats.active_users_last_7_days}
            icon={<TrendingUpIcon />}
            color={theme.palette.success.main}
            subtitle={`${activeUserRate}% of total users`}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Admin Users"
            value={stats.admin_users}
            icon={<SecurityIcon />}
            color={theme.palette.error.main}
            subtitle="System administrators"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Total Datasets"
            value={stats.total_datasets}
            icon={<StorageIcon />}
            color={theme.palette.info.main}
            subtitle={`${stats.users_with_datasets} users with data`}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Pro Users"
            value={stats.pro_users + stats.edu_pro_users}
            icon={<AssessmentIcon />}
            color={theme.palette.warning.main}
            subtitle="Paid subscriptions"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="New Users (30d)"
            value={stats.users_last_30_days}
            icon={<ScheduleIcon />}
            color={theme.palette.secondary.main}
            subtitle={`Growth rate: ${userGrowthRate}%`}
          />
        </Grid>
      </Grid>

      {/* Account Type Breakdown */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Account Type Distribution
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Standard</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.standard_users} ({((stats.standard_users / stats.total_users) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.standard_users / stats.total_users) * 100}
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Pro</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.pro_users} ({((stats.pro_users / stats.total_users) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.pro_users / stats.total_users) * 100}
                  color="warning"
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Educational</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.edu_users} ({((stats.edu_users / stats.total_users) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.edu_users / stats.total_users) * 100}
                  color="info"
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Educational Pro</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.edu_pro_users} ({((stats.edu_pro_users / stats.total_users) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.edu_pro_users / stats.total_users) * 100}
                  color="secondary"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                User Engagement
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Data Usage Rate</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {datasetUsageRate}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={parseFloat(datasetUsageRate)}
                  color="success"
                  sx={{ mb: 3, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Weekly Active Rate</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {activeUserRate}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={parseFloat(activeUserRate)}
                  color="primary"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminOverview;
