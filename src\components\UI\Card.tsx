import React from 'react';
import { 
  Card as MuiCard, 
  CardProps as MuiCardProps, 
  styled 
} from '@mui/material';

interface CardProps extends MuiCardProps {
  hoverEffect?: boolean;
  borderHighlight?: boolean;
  gradient?: boolean;
}

const StyledCard = styled(MuiCard, {
  shouldForwardProp: (prop) =>
    prop !== 'hoverEffect' && prop !== 'borderHighlight' && prop !== 'gradient',
})<CardProps>(({ theme, hoverEffect, borderHighlight, gradient }) => ({
  borderRadius: theme.spacing(1.5),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 2px 12px rgba(0, 0, 0, 0.3)'
    : '0 2px 12px rgba(0, 0, 0, 0.05)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  // overflow: 'hidden', // Allow content to determine overflow, parent might handle scrolling
  height: 'auto', // Allow card to grow with its content
  position: 'relative',

  // Optional hover effect
  ...(hoverEffect && {
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: theme.palette.mode === 'dark'
        ? '0 12px 20px rgba(0, 0, 0, 0.4)'
        : '0 12px 20px rgba(0, 0, 0, 0.1)',
    },
  }),

  // Optional border highlight
  ...(borderHighlight && {
    borderLeft: `4px solid ${theme.palette.primary.main}`,
  }),

  // Optional gradient background
  ...(gradient && {
    backgroundImage: theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[800]} 100%)`
      : `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
  }),
}));

const Card: React.FC<CardProps> = ({ 
  children, 
  hoverEffect = false,
  borderHighlight = false,
  gradient = false,
  ...rest 
}) => {
  return (
    <StyledCard 
      hoverEffect={hoverEffect} 
      borderHighlight={borderHighlight}
      gradient={gradient}
      {...rest}
    >
      {children}
    </StyledCard>
  );
};

export default Card;
