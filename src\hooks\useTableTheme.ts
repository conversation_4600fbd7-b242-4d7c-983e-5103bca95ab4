import { useTheme } from '@mui/material/styles';
import { SxProps, Theme } from '@mui/material';

/**
 * Custom hook that provides consistent table styling for both light and dark themes
 * This ensures all tables across the application have proper readability and contrast
 */
export const useTableTheme = () => {
  const theme = useTheme();

  // Base table cell styling that works in both themes
  const tableCellBase: SxProps<Theme> = {
    padding: '12px 16px',
    borderBottom: theme.palette.mode === 'dark' 
      ? '1px solid rgba(255, 255, 255, 0.15)' 
      : '1px solid rgba(0, 0, 0, 0.12)',
    color: theme.palette.text.primary,
  };

  // Table header cell styling with proper contrast
  const tableHeaderCell: SxProps<Theme> = {
    ...tableCellBase,
    fontWeight: 600,
    backgroundColor: theme.palette.mode === 'dark' 
      ? theme.palette.grey[800] // #2a2a2a
      : theme.palette.grey[50],
    color: theme.palette.text.primary,
    borderBottom: theme.palette.mode === 'dark'
      ? '2px solid rgba(255, 255, 255, 0.2)'
      : '2px solid rgba(0, 0, 0, 0.12)',
  };

  // Table row styling with hover and alternating colors
  const tableRow: SxProps<Theme> = {
    transition: 'background-color 0.2s',
    '&:last-child td': {
      borderBottom: 0,
    },
    '&:nth-of-type(odd)': {
      backgroundColor: theme.palette.mode === 'dark'
        ? 'rgba(255, 255, 255, 0.04)'
        : 'rgba(0, 0, 0, 0.02)',
    },
    '&:hover': {
      backgroundColor: theme.palette.mode === 'dark'
        ? 'rgba(255, 255, 255, 0.08)'
        : 'rgba(0, 0, 0, 0.04)',
    },
  };

  // Special styling for totals/summary rows
  const totalRow: SxProps<Theme> = {
    ...tableRow,
    backgroundColor: theme.palette.mode === 'dark'
      ? theme.palette.grey[800]
      : theme.palette.grey[50],
    fontWeight: 600,
    '&:hover': {
      backgroundColor: theme.palette.mode === 'dark'
        ? theme.palette.grey[700]
        : theme.palette.grey[100],
    },
  };

  // Special styling for significant results (p < 0.05)
  const significantRow: SxProps<Theme> = {
    ...tableRow,
    backgroundColor: theme.palette.mode === 'dark'
      ? 'rgba(76, 175, 80, 0.08)'
      : 'rgba(76, 175, 80, 0.05)',
    '&:hover': {
      backgroundColor: theme.palette.mode === 'dark'
        ? 'rgba(76, 175, 80, 0.12)'
        : 'rgba(76, 175, 80, 0.08)',
    },
  };

  // Table container styling
  const tableContainer: SxProps<Theme> = {
    backgroundColor: theme.palette.background.paper,
    borderRadius: 1,
    border: theme.palette.mode === 'dark'
      ? '1px solid rgba(255, 255, 255, 0.12)'
      : '1px solid rgba(0, 0, 0, 0.12)',
  };

  return {
    tableCellBase,
    tableHeaderCell,
    tableRow,
    totalRow,
    significantRow,
    tableContainer,
    // Convenience functions for common patterns
    getHeaderCellSx: (additionalSx?: SxProps<Theme>) => ({
      ...tableHeaderCell,
      ...additionalSx,
    }),
    getCellSx: (additionalSx?: SxProps<Theme>) => ({
      ...tableCellBase,
      ...additionalSx,
    }),
    getRowSx: (isSignificant?: boolean, isTotal?: boolean, additionalSx?: SxProps<Theme>) => ({
      ...(isTotal ? totalRow : isSignificant ? significantRow : tableRow),
      ...additionalSx,
    }),
  };
};

/**
 * Utility function to get theme-aware colors for statistical significance
 */
export const useStatisticalColors = () => {
  const theme = useTheme();

  return {
    significant: {
      color: theme.palette.success.main,
      backgroundColor: theme.palette.mode === 'dark'
        ? 'rgba(76, 175, 80, 0.08)'
        : 'rgba(76, 175, 80, 0.05)',
    },
    notSignificant: {
      color: theme.palette.text.secondary,
      backgroundColor: 'transparent',
    },
    warning: {
      color: theme.palette.warning.main,
      backgroundColor: theme.palette.mode === 'dark'
        ? 'rgba(255, 152, 0, 0.08)'
        : 'rgba(255, 152, 0, 0.05)',
    },
  };
};
