import { supabase } from './supabaseClient';

export interface SystemStats {
  total_users: number;
  total_profiles: number;
  admin_users: number;
  standard_users: number;
  pro_users: number;
  edu_users: number;
  edu_pro_users: number;
  users_with_datasets: number;
  total_datasets: number;
  users_last_7_days: number;
  users_last_30_days: number;
  active_users_last_7_days: number;
  active_users_last_30_days: number;
}

/**
 * Enhanced function to get user statistics with fallback calculations
 * This function first tries the database function, then supplements with client-side calculations
 */
export const getEnhancedUserStatistics = async (): Promise<SystemStats> => {
  try {
    // First, try the database function
    const { data: dbStats, error: dbError } = await supabase.rpc('get_user_statistics');
    
    if (dbError) {
      console.error('Database function error:', dbError);
      throw dbError;
    }
    
    console.log('📊 Database stats:', dbStats);
    
    // If we got data but some time-based stats are 0, try to supplement them
    if (dbStats && (
      dbStats.users_last_7_days === 0 || 
      dbStats.users_last_30_days === 0 || 
      dbStats.active_users_last_7_days === 0 || 
      dbStats.active_users_last_30_days === 0
    )) {
      console.log('🔄 Supplementing time-based statistics...');
      
      // Try to get additional data that might help
      const supplementalStats = await getSupplementalStats();
      
      // Merge the stats
      const enhancedStats = {
        ...dbStats,
        ...supplementalStats
      };
      
      console.log('✅ Enhanced stats:', enhancedStats);
      return enhancedStats;
    }
    
    return dbStats;
    
  } catch (error) {
    console.error('Error getting enhanced statistics:', error);
    
    // Fallback: calculate everything client-side
    console.log('🔄 Falling back to client-side calculations...');
    return await getClientSideStatistics();
  }
};

/**
 * Get supplemental statistics that can help fill in missing data
 */
const getSupplementalStats = async () => {
  const stats: Partial<SystemStats> = {};
  
  try {
    // Try to get some time-based data from user_datasets (which has created_at)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    // Count datasets created in the last 7 days as a proxy for user activity
    const { count: recentDatasets7d } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', sevenDaysAgo.toISOString());
    
    const { count: recentDatasets30d } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', thirtyDaysAgo.toISOString());
    
    // Use dataset creation as a proxy for user activity
    if (recentDatasets7d !== null) {
      stats.active_users_last_7_days = Math.min(recentDatasets7d, stats.total_users || 0);
    }
    
    if (recentDatasets30d !== null) {
      stats.active_users_last_30_days = Math.min(recentDatasets30d, stats.total_users || 0);
    }
    
    console.log('📊 Supplemental stats:', stats);
    
  } catch (error) {
    console.error('Error getting supplemental stats:', error);
  }
  
  return stats;
};

/**
 * Fallback function to calculate all statistics client-side
 */
const getClientSideStatistics = async (): Promise<SystemStats> => {
  const stats: SystemStats = {
    total_users: 0,
    total_profiles: 0,
    admin_users: 0,
    standard_users: 0,
    pro_users: 0,
    edu_users: 0,
    edu_pro_users: 0,
    users_with_datasets: 0,
    total_datasets: 0,
    users_last_7_days: 0,
    users_last_30_days: 0,
    active_users_last_7_days: 0,
    active_users_last_30_days: 0
  };
  
  try {
    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('accounttype, is_admin');
    
    if (profilesError) throw profilesError;
    
    if (profiles) {
      stats.total_users = profiles.length;
      stats.total_profiles = profiles.length;
      
      // Count by account type
      profiles.forEach(profile => {
        if (profile.is_admin) stats.admin_users++;
        
        switch (profile.accounttype) {
          case 'pro':
            stats.pro_users++;
            break;
          case 'edu':
            stats.edu_users++;
            break;
          case 'edu_pro':
            stats.edu_pro_users++;
            break;
          default:
            stats.standard_users++;
        }
      });
    }
    
    // Get dataset statistics
    const { count: totalDatasets } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true });
    
    const { data: uniqueUsers } = await supabase
      .from('user_datasets')
      .select('user_id')
      .not('user_id', 'is', null);
    
    if (totalDatasets !== null) stats.total_datasets = totalDatasets;
    if (uniqueUsers) {
      const uniqueUserIds = new Set(uniqueUsers.map(u => u.user_id));
      stats.users_with_datasets = uniqueUserIds.size;
    }
    
    // For time-based stats, we'll use dataset creation as a proxy
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const { count: recentActivity } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', sevenDaysAgo.toISOString());
    
    if (recentActivity !== null) {
      stats.active_users_last_7_days = Math.min(recentActivity, stats.total_users);
      stats.users_last_7_days = Math.min(recentActivity, stats.total_users);
    }
    
    console.log('📊 Client-side calculated stats:', stats);
    
  } catch (error) {
    console.error('Error calculating client-side statistics:', error);
  }
  
  return stats;
};
