import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Divider,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  Chip
} from '@mui/material';
import {
  Science as ScienceIcon,
  Help as HelpIcon,
  Check as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import {
  comprehensiveNormalityTest,
  calculateMean,
  calculateMedian,
  calculateStandardDeviation,
  calculateSkewness,
  calculateKurtosis,
  performLeveneTest
} from '@/utils/stats';
import jStat from 'jstat'; // Added jStat import
// Removed unused chart imports for now to simplify, can be added back if group-level charts are implemented
// import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, XAxi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip as RechartsTool<PERSON>, Legend, ResponsiveContainer, LineChart, Line, BarChart, Bar, Cell, ReferenceLine, Label, ComposedChart as RechartsComposedChart } from 'recharts';

// Component for statistical assumption checking
const AssumptionChecker: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedDependentVariables, setSelectedDependentVariables] = useState<string[]>([]);
  const [selectedFactorVariable, setSelectedFactorVariable] = useState<string>('');
  
  const [selectedAssumptions, setSelectedAssumptions] = useState<string[]>([
    'normality',
    'homogeneity',
    'outliers'
  ]);

  const [leveneVariant, setLeveneVariant] = useState<'both' | 'mean' | 'median'>('both');

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<any | null>(null);
  
  const numericColumns = currentDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [];
  const categoricalColumns = currentDataset?.columns.filter(col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN) || [];

  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedDependentVariables([]);
    setSelectedFactorVariable('');
    setResults(null);
    setError(null);
  };
  
  const handleDependentVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedDependentVariables(typeof value === 'string' ? [value] : value);
    setResults(null);
    setError(null);
  };

  const handleFactorVariableChange = (event: SelectChangeEvent<string>) => {
    setSelectedFactorVariable(event.target.value as string);
    setResults(null);
    setError(null);
  };
  
  const handleAssumptionChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedAssumptions(typeof value === 'string' ? [value] : value);
  };
  
  const isFormValid = () => {
    return currentDataset && selectedDependentVariables.length > 0 && selectedFactorVariable && selectedAssumptions.length > 0;
  };
  
  const getLeveneInterpretation = (leveneResult: any) => {
    if (!leveneResult || !leveneResult.df || leveneResult.df.length < 2) return "Levene's test interpretation not available.";
    let interpretation = `Levene's test for homogeneity of variances (alpha = ${leveneResult.alpha.toFixed(2)}): `;
    interpretation += `F(${leveneResult.df[0]}, ${leveneResult.df[1]}) = ${leveneResult.statistic.toFixed(3)}, p = ${leveneResult.pValue.toFixed(3)}. `;
    if (leveneResult.rejected) {
      interpretation += "Assumption of equal variances is NOT met.";
    } else {
      interpretation += "Assumption of equal variances is met.";
    }
    return interpretation;
  };

  const getNormalityInterpretationText = (normalityTest: any, skewness: number, kurtosis: number) => {
    let interpretation = '';
    if (normalityTest.isNormal) {
      interpretation += 'Data appears normally distributed (p > 0.05). ';
    } else {
      interpretation += 'Data does NOT appear normally distributed (p <= 0.05). ';
    }
    if (Math.abs(skewness) < 0.5) interpretation += 'Approx. symmetric. ';
    else if (Math.abs(skewness) < 1) interpretation += `Moderately ${skewness > 0 ? 'positive' : 'negative'} skew. `;
    else interpretation += `Heavily ${skewness > 0 ? 'positive' : 'negative'} skew. `;
    
    const excessKurtosis = kurtosis - 3; // Assuming Mesokurtic (normal) is 3
    if (Math.abs(excessKurtosis) < 0.5) interpretation += 'Normal peak/tails.';
    else if (excessKurtosis > 0) interpretation += 'Leptokurtic (sharp peak, heavy tails).';
    else interpretation += 'Platykurtic (flat peak, thin tails).';
    return interpretation;
  };

  const getOutlierInterpretationText = (outlierCount: number, totalCount: number) => {
    if (outlierCount === 0) return 'No outliers detected.';
    const percentage = (outlierCount / totalCount) * 100;
    return `${outlierCount} outlier(s) detected (${percentage.toFixed(1)}%).`;
  };

  const runAssumptionChecks = () => {
    if (!isFormValid()) {
      setError('Please select dataset, at least one dependent variable, a factor variable, and assumptions.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setResults(null); 
    
    try {
      const factorColumnDef = currentDataset!.columns.find(col => col.id === selectedFactorVariable);
      if (!factorColumnDef) throw new Error("Factor variable definition not found.");
      
      const uniqueFactorLevels = [...new Set(currentDataset!.data.map(row => row[factorColumnDef.name]))]
                                .filter(level => level != null && String(level).trim() !== '');

      if (uniqueFactorLevels.length < 2) {
        throw new Error("Factor variable must have at least two distinct levels with data.");
      }

      const allResults: any = {
        dependentVariablesChecks: {},
        leveneTests: {}
      };

      for (const depVarId of selectedDependentVariables) {
        const depVarColumnDef = currentDataset!.columns.find(col => col.id === depVarId);
        if (!depVarColumnDef) continue;

        allResults.dependentVariablesChecks[depVarId] = {
          name: depVarColumnDef.name,
          groups: {},
          overallAssessment: {} // Initialize overall assessment object
        };

        const groupsDataForLevene: number[][] = [];
        const groupNamesForLevene: string[] = [];

        for (const level of uniqueFactorLevels) {
          const groupValues = currentDataset!.data
            .filter(row => row[factorColumnDef.name] === level)
            .map(row => row[depVarColumnDef.name])
            .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

          if (groupValues.length === 0) {
            allResults.dependentVariablesChecks[depVarId].groups[String(level)] = { name: String(level), error: 'No numeric data for this group.' };
            continue;
          }
          
          if (groupValues.length > 1) { // Levene needs >1 for variance calc within group
             groupsDataForLevene.push(groupValues);
             groupNamesForLevene.push(String(level));
          }


          const groupStats: any = {
            name: String(level),
            n: groupValues.length,
            mean: groupValues.length > 0 ? calculateMean(groupValues) : 'N/A',
            median: groupValues.length > 0 ? calculateMedian(groupValues) : 'N/A',
            sd: groupValues.length > 1 ? calculateStandardDeviation(groupValues) : 'N/A',
          };

          if (selectedAssumptions.includes('normality') && groupValues.length >=3) {
            const normalityTest = comprehensiveNormalityTest(groupValues, 0.05, ['auto']);
            const skewness = calculateSkewness(groupValues);
            const kurtosis = calculateKurtosis(groupValues);
            groupStats.normality = {
              isNormal: normalityTest.overallAssessment.isNormal,
              pValue: normalityTest.tests.shapiroWilk?.pValue ||
                      normalityTest.tests.kolmogorovSmirnov?.pValue ||
                      normalityTest.tests.jarqueBera?.pValue || NaN,
              statistic: normalityTest.tests.shapiroWilk?.statistic ||
                        normalityTest.tests.kolmogorovSmirnov?.statistic ||
                        normalityTest.tests.jarqueBera?.statistic || NaN,
              testUsed: normalityTest.recommendedTest,
              confidence: normalityTest.overallAssessment.confidence,
              skewness,
              kurtosis,
              interpretation: getNormalityInterpretationText(normalityTest, skewness, kurtosis)
            };
          } else if (selectedAssumptions.includes('normality')) {
            groupStats.normality = { error: "Not enough data for normality test (min 3 required)." };
          }

          if (selectedAssumptions.includes('outliers') && groupValues.length > 0) {
            const sortedValues = [...groupValues].sort((a, b) => a - b);
            const q1 = jStat.percentile(sortedValues, 0.25);
            const q3 = jStat.percentile(sortedValues, 0.75);
            const iqr = q3 - q1;
            const lowerBound = q1 - 1.5 * iqr;
            const upperBound = q3 + 1.5 * iqr;
            const outliers = groupValues.filter(val => val < lowerBound || val > upperBound);
            groupStats.outliers = {
              count: outliers.length,
              percentage: (outliers.length / groupValues.length) * 100,
              values: outliers,
              interpretation: getOutlierInterpretationText(outliers.length, groupValues.length)
            };
          }
          allResults.dependentVariablesChecks[depVarId].groups[String(level)] = groupStats;
        }
        
        if (selectedAssumptions.includes('homogeneity')) {
          if (groupsDataForLevene.length >= 2 && groupsDataForLevene.every(g => g.length > 0)) { // Ensure all groups for Levene have data
            try {
              // Run Levene's test based on user selection
              const testResult: any = {
                dependentVariableName: depVarColumnDef.name,
                factorVariableName: factorColumnDef.name,
                groupsTested: groupNamesForLevene
              };

              if (leveneVariant === 'both' || leveneVariant === 'mean') {
                const leveneResultMean = performLeveneTest(...groupsDataForLevene, { variant: 'mean' });
                testResult.meanBased = {
                  statistic: leveneResultMean.statistic,
                  pValue: leveneResultMean.pValue,
                  df: leveneResultMean.df,
                  rejected: leveneResultMean.rejected,
                  alpha: leveneResultMean.alpha,
                  method: leveneResultMean.method,
                  interpretation: getLeveneInterpretation(leveneResultMean)
                };
              }

              if (leveneVariant === 'both' || leveneVariant === 'median') {
                const leveneResultMedian = performLeveneTest(...groupsDataForLevene, { variant: 'median' });
                testResult.medianBased = {
                  statistic: leveneResultMedian.statistic,
                  pValue: leveneResultMedian.pValue,
                  df: leveneResultMedian.df,
                  rejected: leveneResultMedian.rejected,
                  alpha: leveneResultMedian.alpha,
                  method: leveneResultMedian.method,
                  interpretation: getLeveneInterpretation(leveneResultMedian)
                };
              }

              allResults.leveneTests[depVarId] = testResult;
            } catch (e: any) {
              allResults.leveneTests[depVarId] = { error: `Levene's test error for ${depVarColumnDef.name}: ${e.message}` };
            }
          } else {
             allResults.leveneTests[depVarId] = { error: `Not enough groups with sufficient data for ${depVarColumnDef.name} for Levene's test.` };
          }
        }

        // Overall assessment for the dependent variable (ignoring factor)
        const overallDepVarValues = currentDataset!.data
          .map(row => row[depVarColumnDef.name])
          .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

        if (overallDepVarValues.length > 0) {
          if (selectedAssumptions.includes('normality') && overallDepVarValues.length >= 3) {
            const normalityTest = comprehensiveNormalityTest(overallDepVarValues, 0.05, ['auto']);
            const skewness = calculateSkewness(overallDepVarValues);
            const kurtosis = calculateKurtosis(overallDepVarValues);
            allResults.dependentVariablesChecks[depVarId].overallAssessment.normality = {
              isNormal: normalityTest.overallAssessment.isNormal,
              pValue: normalityTest.tests.shapiroWilk?.pValue ||
                      normalityTest.tests.kolmogorovSmirnov?.pValue ||
                      normalityTest.tests.jarqueBera?.pValue || NaN,
              statistic: normalityTest.tests.shapiroWilk?.statistic ||
                        normalityTest.tests.kolmogorovSmirnov?.statistic ||
                        normalityTest.tests.jarqueBera?.statistic || NaN,
              testUsed: normalityTest.recommendedTest,
              confidence: normalityTest.overallAssessment.confidence,
              skewness,
              kurtosis,
              interpretation: getNormalityInterpretationText(normalityTest, skewness, kurtosis)
            };
          } else if (selectedAssumptions.includes('normality')) {
            allResults.dependentVariablesChecks[depVarId].overallAssessment.normality = { error: "Not enough data for overall normality test (min 3 required)." };
          }

          if (selectedAssumptions.includes('outliers')) {
            const sortedValues = [...overallDepVarValues].sort((a, b) => a - b);
            const q1 = jStat.percentile(sortedValues, 0.25);
            const q3 = jStat.percentile(sortedValues, 0.75);
            const iqr = q3 - q1;
            const lowerBound = q1 - 1.5 * iqr;
            const upperBound = q3 + 1.5 * iqr;
            const outliers = overallDepVarValues.filter(val => val < lowerBound || val > upperBound);
            allResults.dependentVariablesChecks[depVarId].overallAssessment.outliers = {
              count: outliers.length,
              percentage: (outliers.length / overallDepVarValues.length) * 100,
              values: outliers,
              interpretation: getOutlierInterpretationText(outliers.length, overallDepVarValues.length),
              hasOutliers: outliers.length > 0 // Added for consistent alert severity
            };
          }
        } else {
          if (selectedAssumptions.includes('normality')) {
            allResults.dependentVariablesChecks[depVarId].overallAssessment.normality = { error: "No numeric data for overall normality test." };
          }
          if (selectedAssumptions.includes('outliers')) {
            allResults.dependentVariablesChecks[depVarId].overallAssessment.outliers = { error: "No numeric data for overall outlier detection." };
          }
        }
      }
      setResults(allResults);
    } catch (err) {
      setError(`Error checking assumptions: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };
    
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>Statistical Assumption Checker</Typography>
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select labelId="dataset-select-label" value={selectedDatasetId} label="Dataset" onChange={handleDatasetChange} disabled={datasets.length === 0}>
                {datasets.map(ds => (<MenuItem key={ds.id} value={ds.id}>{ds.name}</MenuItem>))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dependent-variables-label">Dependent Variables (Numeric)</InputLabel>
              <Select labelId="dependent-variables-label" multiple value={selectedDependentVariables} label="Dependent Variables (Numeric)" onChange={handleDependentVariableChange} disabled={numericColumns.length === 0} renderValue={(selected) => selected.map(id => numericColumns.find(col => col.id === id)?.name || id).join(', ')}>
                {numericColumns.map(column => (<MenuItem key={column.id} value={column.id}>{column.name}</MenuItem>))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="factor-variable-label">Factor Variable (Categorical)</InputLabel>
              <Select labelId="factor-variable-label" value={selectedFactorVariable} label="Factor Variable (Categorical)" onChange={handleFactorVariableChange} disabled={categoricalColumns.length === 0}>
                {categoricalColumns.map(column => (<MenuItem key={column.id} value={column.id}>{column.name}</MenuItem>))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        <Box mt={2}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="assumptions-label">Assumptions to Check</InputLabel>
            <Select labelId="assumptions-label" multiple value={selectedAssumptions} label="Assumptions to Check" onChange={handleAssumptionChange} renderValue={(selected) => selected.join(', ')}>
              <MenuItem value="normality">Normality (Shapiro-Wilk per group)</MenuItem>
              <MenuItem value="homogeneity">Homogeneity of Variances (Levene's Test per dependent variable)</MenuItem>
              <MenuItem value="outliers">Outlier Detection (IQR method per group)</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {selectedAssumptions.includes('homogeneity') && (
          <Box mt={2}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="levene-variant-label">Levene's Test Variant</InputLabel>
              <Select
                labelId="levene-variant-label"
                value={leveneVariant}
                label="Levene's Test Variant"
                onChange={(e) => setLeveneVariant(e.target.value as 'both' | 'mean' | 'median')}
              >
                <MenuItem value="both">Both (Mean-based and Median-based)</MenuItem>
                <MenuItem value="mean">Mean-based (Classic Levene's Test)</MenuItem>
                <MenuItem value="median">Median-based (Brown-Forsythe Test)</MenuItem>
              </Select>
            </FormControl>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              <strong>Recommendation:</strong> Median-based (Brown-Forsythe) is more robust to non-normal distributions.
              Mean-based matches SPSS "Based on Mean" results, while median-based matches SPSS "Based on Median" results.
            </Typography>
          </Box>
        )}

        <Box mt={2}><Button variant="contained" startIcon={<CheckIcon />} onClick={runAssumptionChecks} disabled={loading || !isFormValid()}>Check Assumptions</Button></Box>
      </Paper>
      
      {loading && <CircularProgress sx={{ display: 'block', margin: '20px auto' }} />}
      {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}
      
      {results && !loading && (
        <Box>
          {Object.entries(results.leveneTests).map(([depVarId, leveneRes]: [string, any]) => (
            <Paper key={`levene-${depVarId}`} elevation={2} sx={{ p:2, mb: 3}}>
              <Typography variant="h6" gutterBottom>Homogeneity of Variances (Levene's Test)</Typography>
              <Typography variant="subtitle1" gutterBottom>For Dependent Variable: {leveneRes.dependentVariableName} (across groups of {leveneRes.factorVariableName})</Typography>
              {leveneRes.error ? <Alert severity="warning">{leveneRes.error}</Alert> : (
                <>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Test</TableCell>
                          <TableCell>F</TableCell>
                          <TableCell>df1</TableCell>
                          <TableCell>df2</TableCell>
                          <TableCell>p-value</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {leveneRes.meanBased && (
                          <TableRow>
                            <TableCell>Based on Mean</TableCell>
                            <TableCell>{leveneRes.meanBased.statistic?.toFixed(3)}</TableCell>
                            <TableCell>{leveneRes.meanBased.df ? leveneRes.meanBased.df[0] : 'N/A'}</TableCell>
                            <TableCell>{leveneRes.meanBased.df ? leveneRes.meanBased.df[1] : 'N/A'}</TableCell>
                            <TableCell>{leveneRes.meanBased.pValue?.toFixed(3)}</TableCell>
                          </TableRow>
                        )}
                        {leveneRes.medianBased && (
                          <TableRow>
                            <TableCell>Based on Median</TableCell>
                            <TableCell>{leveneRes.medianBased.statistic?.toFixed(3)}</TableCell>
                            <TableCell>{leveneRes.medianBased.df ? leveneRes.medianBased.df[0] : 'N/A'}</TableCell>
                            <TableCell>{leveneRes.medianBased.df ? leveneRes.medianBased.df[1] : 'N/A'}</TableCell>
                            <TableCell>{leveneRes.medianBased.pValue?.toFixed(3)}</TableCell>
                          </TableRow>
                        )}
                        {/* Fallback for old format */}
                        {!leveneRes.meanBased && !leveneRes.medianBased && (
                          <TableRow>
                            <TableCell>Levene's</TableCell>
                            <TableCell>{leveneRes.statistic?.toFixed(3)}</TableCell>
                            <TableCell>{leveneRes.df ? leveneRes.df[0] : 'N/A'}</TableCell>
                            <TableCell>{leveneRes.df ? leveneRes.df[1] : 'N/A'}</TableCell>
                            <TableCell>{leveneRes.pValue?.toFixed(3)}</TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {leveneRes.meanBased && (
                    <Alert severity={leveneRes.meanBased.rejected ? "warning" : "success"} sx={{mt:1}}>
                      <strong>Mean-based:</strong> {leveneRes.meanBased.interpretation}
                    </Alert>
                  )}

                  {leveneRes.medianBased && (
                    <Alert severity={leveneRes.medianBased.rejected ? "warning" : "success"} sx={{mt:1}}>
                      <strong>Median-based (Brown-Forsythe):</strong> {leveneRes.medianBased.interpretation}
                    </Alert>
                  )}

                  {/* Fallback for old format */}
                  {!leveneRes.meanBased && !leveneRes.medianBased && (
                    <Alert severity={leveneRes.rejected ? "warning" : "success"} sx={{mt:1}}>{leveneRes.interpretation}</Alert>
                  )}

                  <Typography variant="body2" color="text.secondary" sx={{mt:2}}>
                    <strong>Note:</strong> The median-based test (Brown-Forsythe) is more robust to non-normal distributions.
                    Both variants test the null hypothesis that all groups have equal variances.
                  </Typography>
                </>
              )}
            </Paper>
          ))}

          {Object.entries(results.dependentVariablesChecks).map(([depVarId, depVarChecks]: [string, any]) => (
            <Card key={`depVar-${depVarId}`} sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>Assumption Checks for: {depVarChecks.name}</Typography>
                {Object.entries(depVarChecks.groups).map(([groupName, groupRes]: [string, any]) => (
                  <Paper key={`${depVarId}-${groupName}`} elevation={1} sx={{p:2, mb:2}}>
                    <Typography variant="subtitle1" gutterBottom>Group: {groupRes.name || groupName}</Typography>
                    {groupRes.error && <Alert severity="info">{groupRes.error}</Alert>}
                    {!groupRes.error && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}><Typography variant="body2">N: {groupRes.n}</Typography></Grid>
                        <Grid item xs={12} sm={4}><Typography variant="body2">Mean: {typeof groupRes.mean === 'number' ? groupRes.mean.toFixed(3) : groupRes.mean}</Typography></Grid>
                        <Grid item xs={12} sm={4}><Typography variant="body2">SD: {typeof groupRes.sd === 'number' ? groupRes.sd.toFixed(3) : groupRes.sd}</Typography></Grid>
                        
                        {groupRes.normality && (
                          <Grid item xs={12}>
                            <Typography variant="subtitle2">Normality:</Typography>
                            {groupRes.normality.error ? <Alert severity="info">{groupRes.normality.error}</Alert> :
                            <Alert severity={groupRes.normality.isNormal ? "success" : "warning"}>
                              {groupRes.normality.interpretation} (p={groupRes.normality.pValue.toFixed(3)})
                            </Alert>}
                          </Grid>
                        )}
                        {groupRes.outliers && (
                           <Grid item xs={12}>
                            <Typography variant="subtitle2">Outliers:</Typography>
                            <Alert severity={groupRes.outliers.hasOutliers ? "warning" : "success"}>
                              {groupRes.outliers.interpretation}
                              {groupRes.outliers.hasOutliers && ` Values: ${groupRes.outliers.values.map((v:number) => v.toFixed(2)).join(', ')}`}
                            </Alert>
                          </Grid>
                        )}
                      </Grid>
                    )}
                  </Paper>
                ))}

                {/* Overall Assessment Section */}
                {depVarChecks.overallAssessment && (Object.keys(depVarChecks.overallAssessment).length > 0) && (
                  <Paper elevation={1} sx={{
                    p:2,
                    mt: 2,
                    backgroundColor: theme.palette.mode === 'dark'
                      ? theme.palette.grey[800]
                      : theme.palette.grey[100],
                    color: theme.palette.text.primary
                  }}>
                    <Typography variant="subtitle1" gutterBottom sx={{fontWeight: 'bold'}}>
                      Overall Assessment for {depVarChecks.name} (Ignoring Factor Variable)
                    </Typography>
                    {depVarChecks.overallAssessment.normality && (
                      <Box mb={1}>
                        <Typography variant="subtitle2">Overall Normality:</Typography>
                        {depVarChecks.overallAssessment.normality.error ? (
                          <Alert severity="info">{depVarChecks.overallAssessment.normality.error}</Alert>
                        ) : (
                          <Alert severity={depVarChecks.overallAssessment.normality.isNormal ? "success" : "warning"}>
                            {depVarChecks.overallAssessment.normality.interpretation} (p={depVarChecks.overallAssessment.normality.pValue.toFixed(3)}, KS Stat: {depVarChecks.overallAssessment.normality.statistic.toFixed(3)})
                          </Alert>
                        )}
                      </Box>
                    )}
                    {depVarChecks.overallAssessment.outliers && (
                      <Box>
                        <Typography variant="subtitle2">Overall Outliers:</Typography>
                        {depVarChecks.overallAssessment.outliers.error ? (
                           <Alert severity="info">{depVarChecks.overallAssessment.outliers.error}</Alert>
                        ) : (
                          <Alert severity={depVarChecks.overallAssessment.outliers.hasOutliers ? "warning" : "success"}>
                            {depVarChecks.overallAssessment.outliers.interpretation}
                            {depVarChecks.overallAssessment.outliers.hasOutliers && depVarChecks.overallAssessment.outliers.values.length > 0 && 
                              ` Values: ${depVarChecks.overallAssessment.outliers.values.map((v:number) => v.toFixed(2)).join(', ')}`}
                          </Alert>
                        )}
                      </Box>
                    )}
                  </Paper>
                )}
              </CardContent>
            </Card>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default AssumptionChecker;
