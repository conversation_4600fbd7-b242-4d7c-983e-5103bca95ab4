import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Snackbar,

  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  ToggleButton,
  ToggleButtonGroup,
  useMediaQuery,
  Divider,
  Tooltip
} from '@mui/material';
import {
  Science as ScienceIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,

  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Assessment as AssessmentIcon,
  Timeline as TimelineIcon,
  ScatterPlot as ScatterPlotIcon,
  BarChart as BarChartIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  ViewCarousel as ViewCarouselIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import {
  calculateMean,
  calculateStandardDeviation,
  calculateSkewness,
  calculateKurtosis,
  comprehensiveNormalityTest,
  shapiroWilkTest,
  kolmogorovSmirnovTest,
  jarqueBeraTest,
  andersonDarlingTest,
  NormalityTestResult,
  ComprehensiveNormalityResult
} from '@/utils/stats';
import { extractNumericValues, safeSortNumeric } from '@/utils/typeConversions';
import {
  ScatterChart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  ReferenceLine,
  ComposedChart,
  Bar
} from 'recharts';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`normality-tabpanel-${index}`}
      aria-labelledby={`normality-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const NormalityTest: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  
  // State for analysis options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  
  // State for view mode
  const [viewMode, setViewMode] = useState<'tabs' | 'stacked' | 'side-by-side'>('tabs');
  const [activeTab, setActiveTab] = useState<number>(0);
  
  // State for test selection
  const [selectedTests, setSelectedTests] = useState<string[]>(['auto']);
  const [alpha, setAlpha] = useState<number>(0.05);

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [normalityResult, setNormalityResult] = useState<{
    column: any;
    statistics: {
      n: number;
      mean: number;
      standardDeviation: number;
      skewness: number;
      kurtosis: number;
    };
    normalityTests: ComprehensiveNormalityResult;
    qqPlotData: Array<{
      sample: number;
      theoretical: number;
    }>;
    histogramData: Array<{
      bin: string;
      binStart: number;
      binEnd: number;
      frequency: number;
      normalCurve: number;
    }>;
  } | null>(null);
  
  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('normality_test_results');
    
    if (savedResults) {
      try {
        setNormalityResult(JSON.parse(savedResults));
      } catch (e) {
        console.error('Error parsing saved normality test results:', e);
      }
    }
  }, []);

  // Set default view mode based on screen size
  useEffect(() => {
    if (isMobile) {
      setViewMode('tabs');
    } else if (isTablet) {
      setViewMode('stacked');
    }
  }, [isMobile, isTablet]);
  
  // Get numeric columns from current dataset
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedColumn('');
    setNormalityResult(null);
    
    localStorage.removeItem('normality_test_results');
    
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Handle column selection change
  const handleColumnChange = (event: SelectChangeEvent<string>) => {
    setSelectedColumn(event.target.value);
    setNormalityResult(null);
    localStorage.removeItem('normality_test_results');
  };

  // Handle view mode change
  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newViewMode: 'tabs' | 'stacked' | 'side-by-side' | null,
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
      setActiveTab(0);
    }
  };

  // Clear analysis
  const clearAnalysis = () => {
    setNormalityResult(null);
    localStorage.removeItem('normality_test_results');
  };

  // Results context for saving results
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Run normality test
  const runNormalityTest = () => {
    if (!currentDataset || !selectedColumn) {
      setError('Please select a dataset and a numeric column to analyze.');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const column = currentDataset.columns.find(col => col.id === selectedColumn);
      
      if (!column) {
        throw new Error('Selected column not found in dataset.');
      }
      
      const allValues = currentDataset.data.map(row => row[column.name]);
      const numericValues = safeSortNumeric(allValues);
      
      if (numericValues.length === 0) {
        throw new Error('No numeric values found in the selected column.');
      }
      
      // Calculate statistics
      const n = numericValues.length;
      const mean = calculateMean(numericValues);
      const standardDeviation = calculateStandardDeviation(numericValues);
      const skewness = calculateSkewness(numericValues);
      const kurtosis = calculateKurtosis(numericValues);

      // Run comprehensive normality tests
      const normalityTests = comprehensiveNormalityTest(numericValues, alpha, selectedTests);
      
      // Generate Q-Q plot data
      const qqPlotData: Array<{ sample: number; theoretical: number }> = [];
      
      numericValues.forEach((value, index) => {
        const p = (index + 0.5) / n;
        
        // Approximation of the inverse standard normal CDF
        const c0 = 2.515517;
        const c1 = 0.802853;
        const c2 = 0.010328;
        const d1 = 1.432788;
        const d2 = 0.189269;
        const d3 = 0.001308;
        
        let q;
        if (p <= 0.5) {
          const t = Math.sqrt(-2.0 * Math.log(p));
          q = -t + (c0 + c1 * t + c2 * t * t) / (1.0 + d1 * t + d2 * t * t + d3 * t * t * t);
        } else {
          const t = Math.sqrt(-2.0 * Math.log(1.0 - p));
          q = t - (c0 + c1 * t + c2 * t * t) / (1.0 + d1 * t + d2 * t * t + d3 * t * t * t);
        }
        
        const theoreticalValue = mean + standardDeviation * q;
        
        qqPlotData.push({
          sample: value,
          theoretical: theoreticalValue
        });
      });
      
      // Generate histogram data with normal curve overlay
      const min = Math.min(...numericValues);
      const max = Math.max(...numericValues);
      const range = max - min;
      const numBins = Math.max(5, Math.ceil(1 + 3.322 * Math.log10(n)));
      const binWidth = range / numBins;
      
      const histogramData: Array<{
        bin: string;
        binStart: number;
        binEnd: number;
        frequency: number;
        normalCurve: number;
      }> = [];
      
      for (let i = 0; i < numBins; i++) {
        const binStart = min + i * binWidth;
        const binEnd = min + (i + 1) * binWidth;
        const binMidpoint = (binStart + binEnd) / 2;
        
        const binCount = numericValues.filter(
          val => val >= binStart && (i === numBins - 1 ? val <= binEnd : val < binEnd)
        ).length;
        
        const zScore = (binMidpoint - mean) / standardDeviation;
        const normalPdf = (1 / (standardDeviation * Math.sqrt(2 * Math.PI))) * 
                          Math.exp(-(zScore * zScore) / 2);
        
        const scaledNormalValue = normalPdf * n * binWidth;
        
        histogramData.push({
          bin: `${binStart.toFixed(2)}-${binEnd.toFixed(2)}`,
          binStart,
          binEnd,
          frequency: binCount,
          normalCurve: scaledNormalValue
        });
      }
      
      const results = {
        column,
        statistics: {
          n,
          mean,
          standardDeviation,
          skewness,
          kurtosis
        },
        normalityTests,
        qqPlotData,
        histogramData
      };
      
      setNormalityResult(results);
      localStorage.setItem('normality_test_results', JSON.stringify(results));
      setLoading(false);
      
      setSnackbarMessage('Normality test completed successfully!');
      setSnackbarOpen(true);
    } catch (err) {
      setError(`Error analyzing data: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };
  


  // Helper function to get normality interpretation
  const getNormalityInterpretation = (result: any) => {
    if (!result || !result.statistics || !result.normalityTests) return '';

    const { skewness, kurtosis } = result.statistics;
    const { overallAssessment } = result.normalityTests;

    let interpretation = '';

    interpretation += `• Overall Assessment: ${overallAssessment.summary}\n`;
    interpretation += `• Confidence Level: ${overallAssessment.confidence.charAt(0).toUpperCase() + overallAssessment.confidence.slice(1)}\n\n`;
    
    if (Math.abs(skewness) > 1.0) {
      interpretation += `• The distribution shows ${skewness > 0 ? 'strong positive' : 'strong negative'} skewness (${skewness.toFixed(2)}), indicating a significant departure from symmetry.\n`;
    } else if (Math.abs(skewness) > 0.5) {
      interpretation += `• The distribution shows ${skewness > 0 ? 'moderate positive' : 'moderate negative'} skewness (${skewness.toFixed(2)}).\n`;
    } else {
      interpretation += `• The distribution is approximately symmetric (skewness = ${skewness.toFixed(2)}).\n`;
    }
    
    const excessKurtosis = kurtosis - 3;
    if (Math.abs(excessKurtosis) > 1.0) {
      interpretation += `• The distribution has ${excessKurtosis > 0 ? 'heavy tails (leptokurtic)' : 'light tails (platykurtic)'} compared to a normal distribution (kurtosis = ${kurtosis.toFixed(2)}).\n`;
    } else {
      interpretation += `• The distribution has tail weight similar to a normal distribution (kurtosis = ${kurtosis.toFixed(2)}).\n`;
    }
    
    interpretation += '• The Q-Q plot shows how closely the data points follow the theoretical normal distribution. Points falling along the diagonal line indicate normality.\n';
    
    return interpretation;
  };

  // Render normality tests table
  const renderNormalityTestsTable = () => {
    if (!normalityResult?.normalityTests) return null;

    const { tests, overallAssessment, recommendedTest } = normalityResult.normalityTests;
    const testResults = Object.entries(tests).filter(([_, result]) => result && !isNaN(result.pValue));

    return (
      <Box>
        {/* Overall Assessment */}
        <Paper sx={{ p: 3, mb: 3, bgcolor: 'background.paper', borderRadius: 2, border: 1, borderColor: 'divider' }}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
            Overall Assessment
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Chip
              label={overallAssessment.isNormal ? 'Normally Distributed' : 'Not Normally Distributed'}
              color={overallAssessment.isNormal ? 'success' : 'error'}
              size="medium"
              sx={{ fontWeight: 500 }}
            />
            <Chip
              label={`${overallAssessment.confidence.charAt(0).toUpperCase() + overallAssessment.confidence.slice(1)} Confidence`}
              variant="outlined"
              size="small"
            />
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {overallAssessment.summary}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>Recommended Test:</strong> {recommendedTest} (based on sample size: {normalityResult.normalityTests.sampleSize})
          </Typography>
        </Paper>

        {/* Individual Test Results */}
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
          Individual Test Results
        </Typography>
        <TableContainer component={Paper} variant="outlined" sx={{ borderRadius: 2 }}>
          <Table size={isMobile ? "small" : "medium"}>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                  Test
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                  Statistic
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                  p-value
                </TableCell>
                <TableCell align="center" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                  Result
                </TableCell>
                <TableCell sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                  Interpretation
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {testResults.map(([testName, result]) => (
                <TableRow key={testName} sx={{ '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover } }}>
                  <TableCell sx={{ fontWeight: 500 }}>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {result.testName}
                      </Typography>
                      {result.recommendation && (
                        <Typography variant="caption" color="text.secondary">
                          {result.recommendation}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    {result.statistic.toFixed(4)}
                  </TableCell>
                  <TableCell align="right">
                    {result.pValue.toFixed(4)}
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      label={result.isNormal ? 'Normal' : 'Non-Normal'}
                      color={result.isNormal ? 'success' : 'error'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {result.interpretation}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Test Descriptions */}
        <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.paper', borderRadius: 2, border: 1, borderColor: 'divider' }}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
            Test Descriptions
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" sx={{ fontWeight: 500, mb: 1 }}>
                Shapiro-Wilk Test
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Most powerful normality test for small to medium samples (n ≤ 50). Tests the null hypothesis that the data comes from a normal distribution.
              </Typography>

              <Typography variant="subtitle2" sx={{ fontWeight: 500, mb: 1 }}>
                Kolmogorov-Smirnov Test
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Good for larger samples. Compares the empirical distribution function with the theoretical normal distribution.
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" sx={{ fontWeight: 500, mb: 1 }}>
                Jarque-Bera Test
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Based on skewness and kurtosis. Particularly good at detecting departures from normality due to asymmetry or heavy/light tails.
              </Typography>

              <Typography variant="subtitle2" sx={{ fontWeight: 500, mb: 1 }}>
                Anderson-Darling Test
              </Typography>
              <Typography variant="body2" color="text.secondary">
                More sensitive to deviations in the tails of the distribution compared to the Kolmogorov-Smirnov test.
              </Typography>
            </Grid>
          </Grid>
        </Paper>
      </Box>
    );
  };

  // Render statistics table
  const renderStatisticsTable = () => (
    <TableContainer 
      component={Paper} 
      variant="outlined"
      sx={{ borderRadius: 2 }}
    >
      <Table size={isMobile ? "small" : "medium"}>
        <TableHead>
          <TableRow>
            <TableCell sx={{
              fontWeight: 600,
              backgroundColor: theme.palette.mode === 'dark'
                ? theme.palette.grey[800]
                : theme.palette.grey[50],
              color: theme.palette.text.primary
            }}>
              Statistic
            </TableCell>
            <TableCell align="right" sx={{
              fontWeight: 600,
              backgroundColor: theme.palette.mode === 'dark'
                ? theme.palette.grey[800]
                : theme.palette.grey[50],
              color: theme.palette.text.primary
            }}>
              Value
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow sx={{ '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover } }}>
            <TableCell sx={{ fontWeight: 500 }}>Sample Size (N)</TableCell>
            <TableCell align="right">{normalityResult?.statistics.n.toLocaleString()}</TableCell>
          </TableRow>
          <TableRow sx={{ '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover } }}>
            <TableCell sx={{ fontWeight: 500 }}>Mean</TableCell>
            <TableCell align="right">{normalityResult?.statistics.mean.toFixed(4)}</TableCell>
          </TableRow>
          <TableRow sx={{ '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover } }}>
            <TableCell sx={{ fontWeight: 500 }}>Standard Deviation</TableCell>
            <TableCell align="right">{normalityResult?.statistics.standardDeviation.toFixed(4)}</TableCell>
          </TableRow>
          <TableRow sx={{ '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover } }}>
            <TableCell sx={{ fontWeight: 500 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                Skewness
                <Tooltip title="Measure of asymmetry. 0 = symmetric, >0 = right-skewed, <0 = left-skewed">
                  <InfoIcon fontSize="small" color="action" />
                </Tooltip>
              </Box>
            </TableCell>
            <TableCell align="right">{normalityResult?.statistics.skewness.toFixed(4)}</TableCell>
          </TableRow>
          <TableRow sx={{ '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover } }}>
            <TableCell sx={{ fontWeight: 500 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                Kurtosis
                <Tooltip title="Measure of tail heaviness. Normal distribution = 3">
                  <InfoIcon fontSize="small" color="action" />
                </Tooltip>
              </Box>
            </TableCell>
            <TableCell align="right">{normalityResult?.statistics.kurtosis.toFixed(4)}</TableCell>
          </TableRow>
          <TableRow sx={{ backgroundColor: theme.palette.action.selected }}>
            <TableCell sx={{ fontWeight: 700 }}>Overall Assessment</TableCell>
            <TableCell align="right">
              <Chip
                label={normalityResult?.normalityTests.overallAssessment.isNormal
                  ? `Normal (${normalityResult?.normalityTests.overallAssessment.confidence} confidence)`
                  : `Non-Normal (${normalityResult?.normalityTests.overallAssessment.confidence} confidence)`}
                color={normalityResult?.normalityTests.overallAssessment.isNormal ? 'success' : 'error'}
                size="small"
              />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render histogram
  const renderHistogram = () => (
    <Paper 
      variant="outlined" 
      sx={{ 
        p: 2, 
        height: isMobile ? 300 : 400, 
        borderRadius: 2,
        backgroundColor: theme.palette.mode === 'dark'
          ? 'rgba(33, 150, 243, 0.08)'
          : 'rgba(25, 118, 210, 0.03)'
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={normalityResult?.histogramData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: isMobile ? 60 : 80,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis 
            dataKey="bin" 
            angle={isMobile ? -90 : -45} 
            textAnchor="end"
            height={isMobile ? 60 : 80}
            interval={0}
            tick={{ fontSize: isMobile ? 10 : 12 }}
          />
          <YAxis tick={{ fontSize: isMobile ? 10 : 12 }} />
          <RechartsTooltip 
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 8
            }}
          />
          <Legend />
          <Bar 
            dataKey="frequency" 
            fill={theme.palette.primary.main} 
            name="Frequency"
            radius={[4, 4, 0, 0]}
          />
          <Line
            type="monotone"
            dataKey="normalCurve"
            stroke={theme.palette.error.main}
            strokeWidth={2}
            dot={false}
            name="Normal Distribution"
          />
        </ComposedChart>
      </ResponsiveContainer>
    </Paper>
  );

  // Render Q-Q plot
  const renderQQPlot = () => (
    <Paper 
      variant="outlined" 
      sx={{ 
        p: 2, 
        height: isMobile ? 300 : 400, 
        borderRadius: 2,
        backgroundColor: theme.palette.grey[50]
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: isMobile ? 60 : 80,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis 
            type="number" 
            dataKey="theoretical" 
            name="Theoretical Quantiles"
            tick={{ fontSize: isMobile ? 10 : 12 }}
            label={{ 
              value: 'Theoretical Quantiles', 
              position: 'insideBottom',
              offset: isMobile ? -40 : -70
            }}
          />
          <YAxis 
            type="number" 
            dataKey="sample" 
            name="Sample Quantiles"
            tick={{ fontSize: isMobile ? 10 : 12 }}
            label={{ 
              value: 'Sample Quantiles', 
              angle: -90, 
              position: 'insideLeft'
            }}
          />
          <RechartsTooltip 
            cursor={{ strokeDasharray: '3 3' }}
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 8
            }}
          />
          <Scatter 
            name="Q-Q Plot" 
            data={normalityResult?.qqPlotData} 
            fill={theme.palette.primary.main}
          />
          
          {/* Reference line */}
          <ReferenceLine 
            stroke={theme.palette.error.main}
            strokeDasharray="3 3"
            segment={normalityResult?.qqPlotData ? [
              { 
                x: Math.min(...normalityResult.qqPlotData.map(d => d.theoretical)), 
                y: Math.min(...normalityResult.qqPlotData.map(d => d.sample)) 
              },
              { 
                x: Math.max(...normalityResult.qqPlotData.map(d => d.theoretical)), 
                y: Math.max(...normalityResult.qqPlotData.map(d => d.sample)) 
              }
            ] : undefined}
          />
        </ScatterChart>
      </ResponsiveContainer>
    </Paper>
  );
  
  return (
    <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 1400, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
          Normality Testing
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Test whether your data follows a normal distribution using statistical tests and visual diagnostics
        </Typography>
      </Box>

      {/* Configuration Panel */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardHeader 
          title="Configuration"
          avatar={<SettingsIcon color="primary" />}
          sx={{ pb: 1 }}
        />
        <CardContent>
          {/* Dataset and Column Selection */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Dataset</InputLabel>
                <Select
                  value={selectedDatasetId}
                  label="Dataset"
                  onChange={handleDatasetChange}
                  disabled={datasets.length === 0}
                >
                  {datasets.length === 0 ? (
                    <MenuItem value="" disabled>
                      No datasets available
                    </MenuItem>
                  ) : (
                    datasets.map(dataset => (
                      <MenuItem key={dataset.id} value={dataset.id}>
                        <Box>
                          <Typography variant="body2">{dataset.name}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {dataset.data.length} rows
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Numeric Variable</InputLabel>
                <Select
                  value={selectedColumn}
                  label="Numeric Variable"
                  onChange={handleColumnChange}
                  disabled={numericColumns.length === 0}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    numericColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        <Typography variant="body2">{column.name}</Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                          (Numeric)
                        </Typography>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Normality Test Selection */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={8}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                Normality Tests to Run
              </Typography>
              <FormControl fullWidth>
                <InputLabel>Select Tests</InputLabel>
                <Select
                  multiple
                  value={selectedTests}
                  onChange={(e) => setSelectedTests(e.target.value as string[])}
                  label="Select Tests"
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip
                          key={value}
                          label={
                            value === 'auto' ? 'Auto (Recommended)' :
                            value === 'shapiroWilk' ? 'Shapiro-Wilk' :
                            value === 'kolmogorovSmirnov' ? 'Kolmogorov-Smirnov' :
                            value === 'jarqueBera' ? 'Jarque-Bera' :
                            value === 'andersonDarling' ? 'Anderson-Darling' : value
                          }
                          size="small"
                        />
                      ))}
                    </Box>
                  )}
                >
                  <MenuItem value="auto">
                    <Typography variant="body2">
                      <strong>Auto (Recommended)</strong>
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                      Automatically selects the best tests based on sample size
                    </Typography>
                  </MenuItem>
                  <MenuItem value="shapiroWilk">
                    <Typography variant="body2">Shapiro-Wilk Test</Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                      Most powerful for small to medium samples (n ≤ 50)
                    </Typography>
                  </MenuItem>
                  <MenuItem value="kolmogorovSmirnov">
                    <Typography variant="body2">Kolmogorov-Smirnov Test</Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                      Good for larger samples, tests overall distribution shape
                    </Typography>
                  </MenuItem>
                  <MenuItem value="jarqueBera">
                    <Typography variant="body2">Jarque-Bera Test</Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                      Based on skewness and kurtosis, good for detecting specific deviations
                    </Typography>
                  </MenuItem>
                  <MenuItem value="andersonDarling">
                    <Typography variant="body2">Anderson-Darling Test</Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                      More sensitive to deviations in distribution tails
                    </Typography>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                Significance Level (α)
              </Typography>
              <FormControl fullWidth>
                <InputLabel>Alpha Level</InputLabel>
                <Select
                  value={alpha}
                  onChange={(e) => setAlpha(e.target.value as number)}
                  label="Alpha Level"
                >
                  <MenuItem value={0.01}>0.01 (99% confidence)</MenuItem>
                  <MenuItem value={0.05}>0.05 (95% confidence)</MenuItem>
                  <MenuItem value={0.10}>0.10 (90% confidence)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* View Mode Selection */}
          {normalityResult && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                Display Mode
              </Typography>
              <ToggleButtonGroup
                value={viewMode}
                exclusive
                onChange={handleViewModeChange}
                size="small"
                sx={{ mb: 2 }}
              >
                <ToggleButton value="tabs" aria-label="tabs view">
                  <ViewCarouselIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Tabs'}
                </ToggleButton>
                <ToggleButton value="stacked" aria-label="stacked view">
                  <ViewListIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Stacked'}
                </ToggleButton>
                {!isMobile && (
                  <ToggleButton value="side-by-side" aria-label="side by side view">
                    <ViewModuleIcon sx={{ mr: 1 }} />
                    Side by Side
                  </ToggleButton>
                )}
              </ToggleButtonGroup>
              <Typography variant="caption" color="text.secondary" display="block">
                Choose how to display the statistics and visualizations
              </Typography>
            </Box>
          )}

          {/* Test Information */}
          <Accordion defaultExpanded={false}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                About Normality Testing
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary" paragraph>
                Normality testing assesses whether your data follows a normal (Gaussian) distribution. This test includes:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Kolmogorov-Smirnov test:</strong> Statistical test comparing your data to a normal distribution
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Descriptive statistics:</strong> Mean, standard deviation, skewness, and kurtosis
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Q-Q Plot:</strong> Visual comparison of your data quantiles vs. theoretical normal quantiles
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Histogram with normal curve:</strong> Distribution shape comparison
                </Typography>
              </Box>
            </AccordionDetails>
          </Accordion>

          {/* Action Buttons */}
          <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<ScienceIcon />}
              onClick={runNormalityTest}
              disabled={loading || !selectedColumn}
              sx={{ 
                px: 4,
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 600
              }}
            >
              {loading ? 'Testing...' : 'Test for Normality'}
            </Button>
            
            {normalityResult && (
              <Button
                variant="outlined"
                size="large"
                onClick={clearAnalysis}
                sx={{
                  px: 3,
                  borderRadius: 2,
                  textTransform: 'none'
                }}
              >
                Clear Results
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card elevation={2}>
          <CardContent>
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <CircularProgress size={60} sx={{ mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                Testing for Normality...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Calculating statistics and generating plots
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3, borderRadius: 2 }}
          action={
            <Button color="inherit" size="small" onClick={() => setError(null)}>
              Dismiss
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Results */}
      {normalityResult && !loading && (
        <Card elevation={2}>
          <CardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AssessmentIcon color="primary" />
                <Typography variant="h6">
                  Normality Test Results: {normalityResult.column.name}
                </Typography>
              </Box>
            }
            subheader={
              <Typography variant="body2" color="text.secondary">
                Statistical assessment of normal distribution
              </Typography>
            }

          />
          <CardContent>
            {viewMode === 'tabs' ? (
              <Box>
                <Tabs 
                  value={activeTab} 
                  onChange={(_, newValue) => setActiveTab(newValue)}
                  sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
                >
                  <Tab
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <ScienceIcon fontSize="small" />
                        {!isMobile && 'Normality Tests'}
                      </Box>
                    }
                  />
                  <Tab
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <FunctionsIcon fontSize="small" />
                        {!isMobile && 'Statistics'}
                      </Box>
                    }
                  />
                  <Tab
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <BarChartIcon fontSize="small" />
                        {!isMobile && 'Histogram'}
                      </Box>
                    }
                  />
                  <Tab
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <ScatterPlotIcon fontSize="small" />
                        {!isMobile && 'Q-Q Plot'}
                      </Box>
                    }
                  />
                </Tabs>
                <TabPanel value={activeTab} index={0}>
                  {renderNormalityTestsTable()}
                </TabPanel>
                <TabPanel value={activeTab} index={1}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      {renderStatisticsTable()}
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Paper sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 2, border: 1, borderColor: 'divider' }}>
                        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500 }}>
                          Interpretation
                        </Typography>
                        <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-line' }}>
                          {getNormalityInterpretation(normalityResult)}
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                </TabPanel>
                <TabPanel value={activeTab} index={2}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Histogram with Normal Curve Overlay
                  </Typography>
                  {renderHistogram()}
                </TabPanel>
                <TabPanel value={activeTab} index={3}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Q-Q Plot (Normal Probability Plot)
                  </Typography>
                  {renderQQPlot()}
                </TabPanel>
              </Box>
            ) : viewMode === 'stacked' ? (
              <Stack spacing={4}>
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Statistics & Interpretation
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} lg={6}>
                      {renderStatisticsTable()}
                    </Grid>
                    <Grid item xs={12} lg={6}>
                      <Paper sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 2, border: 1, borderColor: 'divider' }}>
                        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500 }}>
                          Interpretation
                        </Typography>
                        <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-line' }}>
                          {getNormalityInterpretation(normalityResult)}
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                </Box>
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Histogram with Normal Curve Overlay
                  </Typography>
                  {renderHistogram()}
                </Box>
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Q-Q Plot (Normal Probability Plot)
                  </Typography>
                  {renderQQPlot()}
                </Box>
              </Stack>
            ) : (
              <Grid container spacing={3}>
                <Grid item xs={12} lg={4}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Statistics
                  </Typography>
                  {renderStatisticsTable()}
                  
                  <Paper sx={{ p: 3, mt: 3, bgcolor: 'background.paper', borderRadius: 2, border: 1, borderColor: 'divider' }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500 }}>
                      Interpretation
                    </Typography>
                    <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-line' }}>
                      {getNormalityInterpretation(normalityResult)}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} lg={4}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Histogram with Normal Curve
                  </Typography>
                  {renderHistogram()}
                </Grid>
                <Grid item xs={12} lg={4}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Q-Q Plot
                  </Typography>
                  {renderQQPlot()}
                </Grid>
              </Grid>
            )}
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && !normalityResult && selectedColumn && (
        <Card elevation={1}>
          <CardContent>
            <Box textAlign="center" py={6}>
              <ScienceIcon sx={{ fontSize: 80, color: theme.palette.grey[400], mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Ready to Test
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Click "Test for Normality" to analyze the distribution of your selected variable
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </Box>
  );
};

export default NormalityTest;
