import { ThemeOptions } from '@mui/material/styles'; // Import ThemeOptions

// Common settings for both themes (typography, shape, components, etc.)
const commonThemeOptions: ThemeOptions = {
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 500,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 500,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 500,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.57,
      letterSpacing: '0.00714em',
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.43,
      letterSpacing: '0.01071em',
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.75,
      letterSpacing: '0.02857em',
      textTransform: 'none',
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.66,
      letterSpacing: '0.03333em',
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 2.66,
      letterSpacing: '0.08333em',
      textTransform: 'uppercase',
    },
  },
  shape: {
    borderRadius: 8,
  },
  mixins: {
    toolbar: {
      minHeight: 64,
      '@media (min-width:0px) and (orientation: landscape)': {
        minHeight: 48,
      },
      '@media (min-width:600px)': {
        minHeight: 64,
      },
    },
  },
  transitions: {
    easing: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
    },
    duration: {
      shortest: 150,
      shorter: 200,
      short: 250,
      standard: 300,
      complex: 375,
      enteringScreen: 225,
      leavingScreen: 195,
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0px 2px 4px -1px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: 8,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-2px)',
          },
        },
        contained: {
          boxShadow: '0px 3px 6px -1px rgba(0,0,0,0.1), 0px 2px 4px -1px rgba(0,0,0,0.06)',
          '&:hover': {
            boxShadow: '0px 6px 10px -1px rgba(0,0,0,0.1), 0px 4px 6px -1px rgba(0,0,0,0.06)',
          },
          '&:active': {
            boxShadow: '0px 1px 2px 0px rgba(0,0,0,0.05)',
            transform: 'translateY(0)',
          },
        },
        outlined: {
          '&:hover': {
            boxShadow: '0px 2px 4px -1px rgba(0,0,0,0.1)',
          },
        },
        text: {
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)', // For light theme
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0px 3px 8px -1px rgba(0,0,0,0.05), 0px 4px 6px -1px rgba(0,0,0,0.01)',
          overflow: 'hidden',
          transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            boxShadow: '0px 8px 16px -2px rgba(0,0,0,0.1), 0px 6px 12px -2px rgba(0,0,0,0.06)',
          },
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 16,
          '&:last-child': {
            paddingBottom: 16,
          },
        },
      },
    },
    MuiCardActions: {
      styleOverrides: {
        root: {
          padding: '8px 16px 16px',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        rounded: {
          borderRadius: 12,
        },
        elevation1: {
          boxShadow: '0px 2px 8px -1px rgba(0,0,0,0.05), 0px 4px 6px -1px rgba(0,0,0,0.01)',
        },
        elevation2: {
          boxShadow: '0px 4px 12px -1px rgba(0,0,0,0.08), 0px 6px 8px -1px rgba(0,0,0,0.02)',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: '12px 16px',
          borderBottom: '1px solid rgba(0, 0, 0, 0.06)', // For light theme
        },
        head: {
          fontWeight: 600,
          backgroundColor: 'rgba(0, 0, 0, 0.02)', // For light theme
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          transition: 'background-color 0.2s',
          '&:last-child td': {
            borderBottom: 0,
          },
          '&:nth-of-type(odd)': {
            backgroundColor: 'rgba(0, 0, 0, 0.01)', // For light theme
          },
          '&:hover': {
            backgroundColor: 'rgba(25, 118, 210, 0.04)', // For light theme
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            transition: 'box-shadow 0.2s',
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: 'rgba(0, 0, 0, 0.23)', // For light theme
            },
            '&.Mui-focused': {
              boxShadow: '0px 0px 0px 2px rgba(25, 118, 210, 0.2)',
              '& .MuiOutlinedInput-notchedOutline': {
                border: '1px solid #1976d2',
              },
            },
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          transition: 'box-shadow 0.2s',
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(0, 0, 0, 0.23)', // For light theme
          },
          '&.Mui-focused': {
            boxShadow: '0px 0px 0px 2px rgba(25, 118, 210, 0.2)',
            '& .MuiOutlinedInput-notchedOutline': {
              border: '1px solid #1976d2',
            },
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          transition: 'background-color 0.2s',
          '&:hover': {
            backgroundColor: 'rgba(25, 118, 210, 0.04)', // For light theme
          },
        },
      },
    },
    MuiRadio: {
      styleOverrides: {
        root: {
          transition: 'background-color 0.2s',
          '&:hover': {
            backgroundColor: 'rgba(25, 118, 210, 0.04)', // For light theme
          },
        },
      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          padding: 8,
          '& .MuiSwitch-track': {
            borderRadius: 12,
          },
          '& .MuiSwitch-thumb': {
            boxShadow: '0px 2px 1px -1px rgba(0,0,0,0.2)',
            transition: 'transform 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          },
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          transition: 'color 0.2s',
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        indicator: {
          height: 3,
          borderTopLeftRadius: 3,
          borderTopRightRadius: 3,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 4,
          margin: '2px 0',
          transition: 'background-color 0.2s',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)', // For light theme
          },
        },
      },
    },
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          minWidth: 40,
          color: 'inherit',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          // backgroundColor: '#ffffff', // Specific to light theme
          border: 'none',
          boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          // backgroundColor: '#ffffff', // Specific to light theme
          transition: 'box-shadow 0.2s',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          fontWeight: 500,
        },
        colorPrimary: {
          backgroundColor: 'rgba(25, 118, 210, 0.1)', // For light theme
          color: '#1976d2',
          '&.MuiChip-outlined': {
            borderColor: 'rgba(25, 118, 210, 0.3)', // For light theme
          },
        },
        colorSecondary: {
          backgroundColor: 'rgba(220, 0, 78, 0.1)', // For light theme
          color: '#dc004e',
          '&.MuiChip-outlined': {
            borderColor: 'rgba(220, 0, 78, 0.3)', // For light theme
          },
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: 'rgba(33, 33, 33, 0.9)', // Dark tooltip for both themes often works well
          borderRadius: 6,
          fontSize: '0.75rem',
          padding: '6px 10px',
        },
        arrow: {
          color: 'rgba(33, 33, 33, 0.9)',
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
        standardSuccess: {
          backgroundColor: 'rgba(76, 175, 80, 0.1)', // For light theme
          color: '#388e3c',
        },
        standardInfo: {
          backgroundColor: 'rgba(33, 150, 243, 0.1)', // For light theme
          color: '#1976d2',
        },
        standardWarning: {
          backgroundColor: 'rgba(255, 152, 0, 0.1)', // For light theme
          color: '#f57c00',
        },
        standardError: {
          backgroundColor: 'rgba(244, 67, 54, 0.1)', // For light theme
          color: '#d32f2f',
        },
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: 'rgba(0, 0, 0, 0.06)', // For light theme
        },
      },
    },
  },
};

export const lightThemeOptions: ThemeOptions = {
  ...commonThemeOptions,
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
      light: '#4791db', // Lighter shade for primary
      dark: '#115293',  // Darker shade for primary
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#dc004e', // Example secondary color
      light: '#e33371',
      dark: '#9a0036',
      contrastText: '#ffffff',
    },
    background: {
      default: '#f5f5f5', // Light background
      paper: '#ffffff',   // Light paper
    },
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
      disabled: 'rgba(0, 0, 0, 0.38)',
    },
    // ... other light palette colors (error, warning, info, success, grey)
    error: { main: '#f44336' },
    warning: { main: '#ff9800' },
    info: { main: '#2196f3' },
    success: { main: '#4caf50' },
  },
  components: {
    ...commonThemeOptions.components, // Spread common component overrides
    // Light theme specific component overrides can go here if needed
    MuiDrawer: {
      styleOverrides: {
        paper: {
          ...(commonThemeOptions.components?.MuiDrawer?.styleOverrides?.paper as object),
          backgroundColor: '#ffffff',
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          ...(commonThemeOptions.components?.MuiInputBase?.styleOverrides?.root as object),
          backgroundColor: '#ffffff',
        },
      },
    },
  }
};

export const darkThemeOptions: ThemeOptions = {
  ...commonThemeOptions,
  palette: {
    mode: 'dark',
    primary: {
      main: '#90caf9', // Lighter primary for dark mode
      light: '#e3f2fd',
      dark: '#42a5f5',
      contrastText: 'rgba(0, 0, 0, 0.87)',
    },
    secondary: {
      main: '#f48fb1', // Lighter secondary for dark mode
      light: '#f8bbd0',
      dark: '#ec407a',
      contrastText: 'rgba(0, 0, 0, 0.87)',
    },
    background: {
      default: '#0a0a0a', // Darker background for better contrast
      paper: '#1a1a1a',   // Slightly lighter dark paper
    },
    text: {
      primary: 'rgba(255, 255, 255, 0.95)', // Higher contrast for primary text
      secondary: 'rgba(255, 255, 255, 0.75)', // Better contrast for secondary text
      disabled: 'rgba(255, 255, 255, 0.5)',
    },
    // Improved dark palette colors for better visibility
    error: { main: '#ff5252' }, // Brighter error for dark mode
    warning: { main: '#ffb74d' }, // Brighter warning for dark mode
    info: { main: '#4fc3f7' }, // Brighter info for dark mode
    success: { main: '#81c784' }, // Brighter success for dark mode
    grey: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#eeeeee',
      300: '#e0e0e0',
      400: '#bdbdbd',
      500: '#9e9e9e',
      600: '#757575',
      700: '#424242',
      800: '#2a2a2a', // Lighter for better contrast in dark mode
      900: '#1a1a1a',
    },
  },
  components: {
    ...commonThemeOptions.components, // Spread common component overrides
    // Dark theme specific component overrides
    MuiButton: {
      styleOverrides: {
        ...(commonThemeOptions.components?.MuiButton?.styleOverrides as object),
        text: {
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
        },
      }
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: '12px 16px',
          borderBottom: '1px solid rgba(255, 255, 255, 0.15) !important', // Visible borders for dark theme
          color: 'rgba(255, 255, 255, 0.95) !important', // High contrast text
          backgroundColor: 'transparent !important', // Ensure no background conflicts
          // Override any hardcoded backgroundColor from components
          '&[style*="background-color"]': {
            backgroundColor: 'transparent !important',
          },
        },
        head: {
          fontWeight: '600 !important',
          backgroundColor: '#2a2a2a !important', // Dark but visible header background
          color: 'rgba(255, 255, 255, 0.95) !important', // High contrast header text
          borderBottom: '2px solid rgba(255, 255, 255, 0.2) !important', // More prominent header border
          // Override any hardcoded styles from components
          '&[style*="background-color"]': {
            backgroundColor: '#2a2a2a !important',
          },
          '&[style*="color"]': {
            color: 'rgba(255, 255, 255, 0.95) !important',
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          transition: 'background-color 0.2s',
          '&:last-child td': {
            borderBottom: 0,
          },
          '&:nth-of-type(odd)': {
            backgroundColor: 'rgba(255, 255, 255, 0.04)', // Subtle alternating rows for dark theme
          },
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)', // Visible but not overwhelming hover
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          ...(commonThemeOptions.components?.MuiTextField?.styleOverrides?.root as object),
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#2b2b2b', // Slightly lighter than paper for contrast
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: 'rgba(255, 255, 255, 0.3)',
            },
            '&.Mui-focused': {
              boxShadow: '0px 0px 0px 2px rgba(144, 202, 249, 0.3)', // Primary color focus ring
              '& .MuiOutlinedInput-notchedOutline': {
                border: '1px solid #90caf9', // Primary color
              },
            },
          },
          '& .MuiInputLabel-root': {
            color: 'rgba(255, 255, 255, 0.7)',
            '&.Mui-focused': {
              color: '#90caf9', // Primary color
            },
          },
        },
      },
    },
     MuiOutlinedInput: {
      styleOverrides: {
        root: {
          ...(commonThemeOptions.components?.MuiOutlinedInput?.styleOverrides?.root as object),
           backgroundColor: '#2b2b2b',
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(255, 255, 255, 0.3)',
          },
          '&.Mui-focused': {
            boxShadow: '0px 0px 0px 2px rgba(144, 202, 249, 0.3)',
            '& .MuiOutlinedInput-notchedOutline': {
              border: '1px solid #90caf9',
            },
          },
        },
         notchedOutline: {
          borderColor: 'rgba(255, 255, 255, 0.23)',
        },
        input:{
          color: '#fff'
        }
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          ...(commonThemeOptions.components?.MuiCheckbox?.styleOverrides?.root as object),
          color: 'rgba(255, 255, 255, 0.7)',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
          '&.Mui-checked':{
            color: '#90caf9', // Primary color
          }
        },
      },
    },
    MuiRadio: {
      styleOverrides: {
        root: {
          ...(commonThemeOptions.components?.MuiRadio?.styleOverrides?.root as object),
          color: 'rgba(255, 255, 255, 0.7)',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
           '&.Mui-checked':{
            color: '#90caf9', // Primary color
          }
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          ...(commonThemeOptions.components?.MuiListItemButton?.styleOverrides?.root as object),
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          ...(commonThemeOptions.components?.MuiDrawer?.styleOverrides?.paper as object),
          backgroundColor: '#1a1a1a', // Consistent with paper background
          borderRight: '1px solid rgba(255, 255, 255, 0.12)',
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          ...(commonThemeOptions.components?.MuiInputBase?.styleOverrides?.root as object),
          backgroundColor: '#2b2b2b', // Darker input background
          color: 'rgba(255, 255, 255, 0.95)', // Better text contrast
          '&:hover': {
            backgroundColor: '#333333',
          },
          '&.Mui-focused': {
            backgroundColor: '#333333',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        ...(commonThemeOptions.components?.MuiChip?.styleOverrides as object),
        colorPrimary: {
          backgroundColor: 'rgba(144, 202, 249, 0.16)', // Primary color with alpha for dark
          color: '#90caf9', // Primary text for dark
          '&.MuiChip-outlined': {
            borderColor: 'rgba(144, 202, 249, 0.3)',
          },
        },
        colorSecondary: {
          backgroundColor: 'rgba(244, 143, 177, 0.16)', // Secondary color with alpha for dark
          color: '#f48fb1', // Secondary text for dark
          '&.MuiChip-outlined': {
            borderColor: 'rgba(244, 143, 177, 0.3)',
          },
        },
      }
    },
    MuiAlert: {
      styleOverrides: {
        ...(commonThemeOptions.components?.MuiAlert?.styleOverrides as object),
        standardSuccess: {
          backgroundColor: 'rgba(102, 187, 106, 0.16)', color: '#66bb6a'
        },
        standardInfo: {
          backgroundColor: 'rgba(41, 182, 246, 0.16)', color: '#29b6f6'
        },
        standardWarning: {
          backgroundColor: 'rgba(255, 167, 38, 0.16)', color: '#ffa726'
        },
        standardError: {
          backgroundColor: 'rgba(244, 67, 54, 0.16)', color: '#ef5350'
        },
      }
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          ...(commonThemeOptions.components?.MuiDivider?.styleOverrides?.root as object),
          borderColor: 'rgba(255, 255, 255, 0.12)',
        },
      },
    },
  },
};

// Note: responsiveFontSizes should be applied when createTheme is called in ThemeContext,
// not here on the options objects.
// export default theme; // Remove default export of a single theme
