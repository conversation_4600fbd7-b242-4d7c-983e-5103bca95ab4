import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Tooltip, 
  styled, 
  Theme,
  useTheme
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  tooltip?: string;
  icon?: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  trendLabel?: string;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  variant?: 'default' | 'gradient' | 'outlined';
}

interface StyledCardProps {
  cardColor: string;
  textColor: string;
  variant: 'default' | 'gradient' | 'outlined';
  theme: Theme;
}

const StyledCard = styled(Paper, {
  shouldForwardProp: (prop) => 
    !['cardColor', 'textColor', 'variant'].includes(prop as string),
})<StyledCardProps>(({ theme, cardColor, textColor, variant }) => ({
  padding: theme.spacing(2.5),
  borderRadius: theme.spacing(1.5),
  height: '100%',
  position: 'relative',
  overflow: 'hidden',
  
  ...(variant === 'default' && {
    backgroundColor: cardColor,
    color: textColor,
  }),
  
  ...(variant === 'gradient' && {
    background: theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${cardColor} 0%, ${theme.palette.background.paper} 100%)`
      : `linear-gradient(135deg, ${cardColor} 0%, ${theme.palette.background.paper} 100%)`,
    color: textColor,
  }),
  
  ...(variant === 'outlined' && {
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    border: `1px solid ${cardColor}`,
    borderLeft: `4px solid ${cardColor}`,
  }),
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 10,
  right: 10,
  opacity: 0.3,
  fontSize: 60,
  lineHeight: 1,
}));

const getTrendIcon = (trend: 'up' | 'down' | 'neutral') => {
  switch (trend) {
    case 'up':
      return <TrendingUpIcon fontSize="small" />;
    case 'down':
      return <TrendingDownIcon fontSize="small" />;
    case 'neutral':
    default:
      return <TrendingFlatIcon fontSize="small" />;
  }
};

const getTrendColor = (trend: 'up' | 'down' | 'neutral', theme: Theme) => {
  switch (trend) {
    case 'up':
      return theme.palette.success.main;
    case 'down':
      return theme.palette.error.main;
    case 'neutral':
    default:
      return theme.palette.grey[500];
  }
};

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  description,
  tooltip,
  icon,
  trend,
  trendValue,
  trendLabel,
  color = 'primary',
  variant = 'default',
}) => {
  const theme = useTheme();
  
  // Get color based on variant with dark theme considerations
  const getCardColor = () => {
    if (variant === 'outlined') return theme.palette[color]?.main || theme.palette.primary.main;

    if (variant === 'default') {
      // For dark theme, use slightly muted colors for better readability
      if (theme.palette.mode === 'dark') {
        const colorMap = {
          primary: theme.palette.primary.dark,
          secondary: theme.palette.secondary.dark,
          success: theme.palette.success.dark,
          error: theme.palette.error.dark,
          warning: theme.palette.warning.dark,
          info: theme.palette.info.dark,
        };
        return colorMap[color] || theme.palette.primary.dark;
      }
      return theme.palette[color]?.main || theme.palette.primary.main;
    }

    // Gradient variant
    return theme.palette[color]?.light || theme.palette.primary.light;
  };

  const getTextColor = () => {
    if (variant === 'outlined') return theme.palette.text.primary;

    if (variant === 'default') {
      // Ensure good contrast for dark theme
      if (theme.palette.mode === 'dark') {
        return 'rgba(255, 255, 255, 0.95)';
      }
      return theme.palette[color]?.contrastText || '#ffffff';
    }

    // Gradient variant uses theme text color
    return theme.palette.text.primary;
  };
  
  const cardColor = getCardColor();
  const textColor = getTextColor();
  
  return (
    <StyledCard 
      elevation={variant === 'outlined' ? 0 : 1}
      cardColor={cardColor} 
      textColor={textColor}
      variant={variant}
      theme={theme}
    >
      <Box sx={{ position: 'relative', zIndex: 1 }}>
        <Typography 
          variant="subtitle2" 
          component="h2" 
          color={variant === 'outlined' ? 'text.secondary' : 'inherit'}
          sx={{ 
            opacity: variant === 'default' ? 0.8 : 0.7,
            fontWeight: 500,
            mb: 1
          }}
        >
          {title}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'flex-end', mb: description ? 1 : 0 }}>
          <Typography 
            variant="h4" 
            component="div" 
            sx={{ 
              fontWeight: 600,
              mr: 1,
              color: variant === 'outlined' ? theme.palette[color]?.main : 'inherit'
            }}
          >
            {value}
          </Typography>
          
          {trend && trendValue && (
            <Box 
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                color: getTrendColor(trend, theme),
                mb: 0.5
              }}
            >
              {getTrendIcon(trend)}
              <Typography 
                variant="body2" 
                component="span" 
                sx={{ ml: 0.5, fontWeight: 500 }}
              >
                {trendValue}
              </Typography>
            </Box>
          )}
        </Box>
        
        {description && (
          <Tooltip title={tooltip || ''} arrow placement="top">
            <Typography 
              variant="body2" 
              component="p" 
              sx={{ 
                opacity: variant === 'default' ? 0.7 : 0.6,
                maxWidth: '80%'
              }}
            >
              {description}
            </Typography>
          </Tooltip>
        )}
        
        {trendLabel && (
          <Typography 
            variant="caption" 
            sx={{ 
              display: 'block', 
              mt: 1,
              opacity: 0.7
            }}
          >
            {trendLabel}
          </Typography>
        )}
      </Box>
      
      {icon && (
        <IconWrapper color={variant === 'outlined' ? 'rgba(0,0,0,0.08)' : 'inherit'}>
          {icon}
        </IconWrapper>
      )}
    </StyledCard>
  );
};

export default StatsCard;