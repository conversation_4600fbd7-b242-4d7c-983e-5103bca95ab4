import { supabase } from '../lib/supabase';

export const runUserStatisticsMigration = async () => {
  const migrationSQL = `
-- Fix User Statistics Calculations Migration
-- This migration fixes the get_user_statistics function to properly calculate time-based statistics
-- by accessing the auth.users table which contains the actual created_at and last_sign_in_at timestamps

-- Create or replace the get_user_statistics function with proper time-based calculations
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  total_users_count INTEGER;
BEGIN
  -- Only allow admins to call this function
  IF NOT COALESCE((SELECT is_admin FROM public.profiles WHERE id = auth.uid()), false) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Get counts from profiles table
  SELECT COUNT(*) INTO total_users_count FROM public.profiles;
  
  -- Build comprehensive statistics object with proper time-based calculations
  SELECT json_build_object(
    'total_users', total_users_count,
    'total_profiles', total_users_count,
    'admin_users', (SELECT COUNT(*) FROM public.profiles WHERE COALESCE(is_admin, false) = true),
    'standard_users', (SELECT COUNT(*) FROM public.profiles WHERE COALESCE(accounttype, 'standard') = 'standard'),
    'pro_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'pro'),
    'edu_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'edu'),
    'edu_pro_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'edu_pro'),
    'users_with_datasets', COALESCE((SELECT COUNT(DISTINCT user_id) FROM public.user_datasets WHERE user_datasets.user_id IS NOT NULL), 0),
    'total_datasets', COALESCE((SELECT COUNT(*) FROM public.user_datasets), 0),
    -- Fixed time-based statistics using auth.users table
    'users_last_7_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '7 days'), 0),
    'users_last_30_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '30 days'), 0),
    'active_users_last_7_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '7 days'), 0),
    'active_users_last_30_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '30 days'), 0)
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_user_statistics() TO authenticated;
`;

  try {
    console.log('🔄 Running user statistics migration...');
    
    const { data, error } = await supabase.rpc('exec', { sql: migrationSQL });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
    
    console.log('✅ Migration completed successfully');
    return { success: true, data };
    
  } catch (err) {
    console.error('❌ Migration error:', err);
    return { success: false, error: err };
  }
};
