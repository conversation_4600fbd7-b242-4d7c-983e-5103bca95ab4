import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Divider,
  TextField,
  useTheme,
  alpha,
  Alert,
  AlertTitle,
  Fade,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from '@mui/material';
import {
  Science as ScienceIcon,
  PlayArrow as PlayArrowIcon,
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType } from '../../../types';
import { extractCategoricalValuesWithMissingCodes } from '../../../utils/missingDataUtils';
import { DatasetSelector, VariableSelector, AnalysisSteps, AnalysisResultCard } from '../../UI';
import { useTableTheme } from '../../../hooks';
import {
  independentSamplesTTest,
  performLeveneTest,
  comprehensiveNormalityTest,
  calculateMean,
  calculateStandardDeviation
} from '@/utils/stats';

const IndependentTTest: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const { getHeaderCellSx } = useTableTheme();

  const [selectedDataset, setSelectedDataset] = useState<string>(currentDataset?.id || '');

  // Sync selectedDataset with currentDataset changes (including filtered datasets)
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDataset) {
      setSelectedDataset(currentDataset.id);
    }
  }, [currentDataset?.id, selectedDataset]);
  const [dependentVariable, setDependentVariable] = useState<string>('');
  const [groupingVariable, setGroupingVariable] = useState<string>('');
  const [significanceLevel, setSignificanceLevel] = useState<number>(0.05);
  const [equalVariances, setEqualVariances] = useState<string>('auto');
  const [alternativeHypothesis, setAlternativeHypothesis] = useState<string>('two-sided');
  
  const [results, setResults] = useState<any | null>(null);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const dataset = datasets.find(d => d.id === selectedDataset);
  const groupValues = (() => {
    if (!dataset || !groupingVariable) return [];

    const column = dataset.columns.find(col => col.name === groupingVariable);
    if (!column) return [];

    // Use enhanced categorical extraction to respect missing value codes
    const validValues = extractCategoricalValuesWithMissingCodes(dataset.data, column);
    return [...new Set(validValues)];
  })();
  const hasTwoGroups = groupValues.length === 2;
  
  const getGroupValues = (groupValue: any): number[] => {
    if (!dataset || !dependentVariable || !groupingVariable) return [];
    return dataset.data
      .filter(row => row[groupingVariable] === groupValue)
      .map(row => Number(row[dependentVariable]))
      .filter(val => !isNaN(val));
  };
  
  const validateDatasetStep = () => selectedDataset ? true : 'Please select a dataset';
  const validateVariablesStep = () => {
    if (!dependentVariable) return 'Please select a dependent variable';
    if (!groupingVariable) return 'Please select a grouping variable';
    if (!hasTwoGroups) return 'The grouping variable must have exactly 2 groups';
    return true;
  };
  const validateOptionsStep = () => (isNaN(significanceLevel) || significanceLevel <= 0 || significanceLevel >= 1) ? 'Significance level must be between 0 and 1' : true;
  
  const runAnalysis = () => {
    try {
      setError(null);
      setShowResults(false);
      setResults(null);

      if (!dataset || !dependentVariable || !groupingVariable || !hasTwoGroups) {
        throw new Error('Missing required parameters or incorrect group count.');
      }
      
      const group1Values = getGroupValues(groupValues[0]);
      const group2Values = getGroupValues(groupValues[1]);
      
      if (group1Values.length < 2 || group2Values.length < 2) {
        throw new Error('Each group must have at least 2 values for the t-test.');
      }

      let assumeEqualVariancesBasedOnTest = true;
      let currentLeveneResult = null;
      let normalityCheckResults: any = {
        group1: { message: "Normality test requires N >= 3", isNormal: false, pValue: null, statistic: null, criticalValue: null }, // Ensure all expected fields are initialized or optional
        group2: { message: "Normality test requires N >= 3", isNormal: false, pValue: null, statistic: null, criticalValue: null }
      };

      if (group1Values.length >= 3) {
         const normTest1 = comprehensiveNormalityTest(group1Values, 0.05, ['auto']);
         const normResult1 = {
           isNormal: normTest1.overallAssessment.isNormal,
           pValue: normTest1.tests.shapiroWilk?.pValue ||
                   normTest1.tests.kolmogorovSmirnov?.pValue ||
                   normTest1.tests.jarqueBera?.pValue || NaN,
           statistic: normTest1.tests.shapiroWilk?.statistic ||
                     normTest1.tests.kolmogorovSmirnov?.statistic ||
                     normTest1.tests.jarqueBera?.statistic || NaN
         };
         normalityCheckResults.group1 = { ...normResult1, message: undefined };
      }
      if (group2Values.length >= 3) {
         const normTest2 = comprehensiveNormalityTest(group2Values, 0.05, ['auto']);
         const normResult2 = {
           isNormal: normTest2.overallAssessment.isNormal,
           pValue: normTest2.tests.shapiroWilk?.pValue ||
                   normTest2.tests.kolmogorovSmirnov?.pValue ||
                   normTest2.tests.jarqueBera?.pValue || NaN,
           statistic: normTest2.tests.shapiroWilk?.statistic ||
                     normTest2.tests.kolmogorovSmirnov?.statistic ||
                     normTest2.tests.jarqueBera?.statistic || NaN
         };
         normalityCheckResults.group2 = { ...normResult2, message: undefined };
      }

      if (equalVariances === 'auto') {
        if (group1Values.length > 1 && group2Values.length > 1) {
          currentLeveneResult = performLeveneTest(group1Values, group2Values);
          assumeEqualVariancesBasedOnTest = !currentLeveneResult.rejected;
        } else {
          assumeEqualVariancesBasedOnTest = false; 
          currentLeveneResult = { error: "Not enough data in one or both groups for Levene's test (min 2 per group)." };
        }
      } else {
        assumeEqualVariancesBasedOnTest = equalVariances === 'assumed';
      }
      
      const tTestOptions = { 
        equalVariances: assumeEqualVariancesBasedOnTest,
        alpha: significanceLevel,
        alternative: alternativeHypothesis as 'two-sided' | 'less' | 'greater'
      };
      const testResults = independentSamplesTTest(group1Values, group2Values, tTestOptions);
      
      const depVarCol = dataset.columns.find(c => c.id === dependentVariable);
      const groupVarCol = dataset.columns.find(c => c.id === groupingVariable);

      setResults({
        ...testResults,
        group1: { name: String(groupValues[0]), n: group1Values.length, mean: calculateMean(group1Values), sd: calculateStandardDeviation(group1Values) },
        group2: { name: String(groupValues[1]), n: group2Values.length, mean: calculateMean(group2Values), sd: calculateStandardDeviation(group2Values) },
        dependentVariable: depVarCol?.name || dependentVariable,
        groupingVariable: groupVarCol?.name || groupingVariable,
        significanceLevel,
        alternativeHypothesis,
        assumedEqualVariancesFinal: assumeEqualVariancesBasedOnTest,
        levenePerformed: equalVariances === 'auto' ? currentLeveneResult : null,
        normalityCheck: normalityCheckResults,
        timestamp: new Date()
      });
      setShowResults(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setShowResults(false);
    }
  };
  
  const renderResultsChart = () => { 
    if (!results) return null;
    const data = [
      { name: results.group1.name, mean: results.group1.mean, error: results.group1.sd / Math.sqrt(results.group1.n) },
      { name: results.group2.name, mean: results.group2.mean, error: results.group2.sd / Math.sqrt(results.group2.n) }
    ];
    const isSignificant = results.pValue <= results.significanceLevel;
    return (
      <Box component="div" sx={{
        width: '100%',
        height: 300,
        maxWidth: 600,
        mx: 'auto',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: theme.palette.mode === 'dark'
          ? alpha(theme.palette.background.paper, 0.8)
          : alpha(theme.palette.background.paper, 0.5),
        p: 2,
        borderRadius: 1,
        color: theme.palette.text.primary
      }}>
        <Typography variant="h6" gutterBottom textAlign="center">Group Means Comparison</Typography>
        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-around', mt: 2 }}>
          {data.map((item, index) => (
            <Box key={index} sx={{ textAlign: 'center', position: 'relative' }}>
              <Box sx={{ height: 180, width: 60, mx: 'auto', backgroundColor: isSignificant ? (index === 0 ? theme.palette.primary.main : theme.palette.secondary.main) : theme.palette.grey[500], position: 'relative', display: 'flex', alignItems: 'flex-end', justifyContent: 'center', borderRadius: '4px 4px 0 0', transition: 'all 0.3s ease-in-out' }}>
                <Box sx={{ height: `${(item.mean / Math.max(results.group1.mean || 0.001, results.group2.mean || 0.001)) * 100}%`, width: '100%', backgroundColor: isSignificant ? (index === 0 ? theme.palette.primary.main : theme.palette.secondary.main) : theme.palette.grey[500], borderRadius: '4px 4px 0 0' }} />
                <Box sx={{ position: 'absolute', top: -20, width: '100%', height: 20, borderLeft: '1px solid', borderRight: '1px solid', borderTop: '1px solid', borderColor: theme.palette.divider }} />
                <Box sx={{ position: 'absolute', top: -20, left: 0, right: 0, height: 1, backgroundColor: theme.palette.divider }} />
              </Box>
              <Typography variant="body2" sx={{ mt: 1 }}>{item.name}</Typography>
              <Typography variant="body2" fontWeight="bold">{item.mean.toFixed(2)}</Typography>
              <Typography variant="caption" color="text.secondary">(SE ±{item.error.toFixed(2)})</Typography>
            </Box>
          ))}
        </Box>
        {isSignificant && (<Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}><Box sx={{ width: 16, height: 16, borderRadius: '50%', backgroundColor: theme.palette.success.main, mr: 1 }} /><Typography variant="body2">Significant difference (p = {results.pValue.toFixed(4)})</Typography></Box>)}
        {!isSignificant && (<Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}><Box sx={{ width: 16, height: 16, borderRadius: '50%', backgroundColor: theme.palette.error.main, mr: 1 }} /><Typography variant="body2">No significant difference (p = {results.pValue.toFixed(4)})</Typography></Box>)}
      </Box>
    );
  };
  
  const analysisSteps = [
    { label: 'Select Dataset', description: 'Choose the dataset for your analysis', content: (<DatasetSelector value={selectedDataset} onChange={(value) => setSelectedDataset(value)} variant="default" showEmpty={false} required minRows={4} helperText="Select a dataset with sufficient data for the analysis"/>), validation: validateDatasetStep },
    { label: 'Select Variables', description: 'Select a numeric dependent variable and a categorical grouping variable', content: (<Grid container spacing={3}><Grid item xs={12}><VariableSelector label="Dependent Variable (Numeric)" helperText="Select the continuous variable you want to compare between groups" value={dependentVariable} onChange={(value) => setDependentVariable(value as string)} datasetId={selectedDataset} required allowedTypes={[DataType.NUMERIC]} variant="autocomplete" placeholder="Select a numeric variable"/></Grid><Grid item xs={12}><VariableSelector label="Grouping Variable (Categorical)" helperText={hasTwoGroups ? `Groups found: ${groupValues.join(', ')}` : "Select a categorical variable with exactly 2 groups"} value={groupingVariable} onChange={(value) => setGroupingVariable(value as string)} datasetId={selectedDataset} required allowedTypes={[DataType.CATEGORICAL]} variant="autocomplete" placeholder="Select a categorical variable" error={!!groupingVariable && !hasTwoGroups}/></Grid>{groupingVariable && !hasTwoGroups && (<Grid item xs={12}><Alert severity="warning"><AlertTitle>Variable Issue</AlertTitle>The grouping variable must have exactly 2 groups, but it has {groupValues.length} groups. Please select a different variable or transform your data.</Alert></Grid>)}</Grid>), validation: validateVariablesStep },
    { label: 'Set Options', description: 'Configure the analysis options', content: (<Grid container spacing={3}><Grid item xs={12} sm={6}><TextField label="Significance Level (α)" type="number" value={significanceLevel} onChange={(e) => setSignificanceLevel(parseFloat(e.target.value))} inputProps={{ min: 0.001, max: 0.999, step: 0.01 }} fullWidth size="small" helperText="Typical values: 0.05, 0.01, or 0.001"/></Grid><Grid item xs={12} sm={6}><FormControl component="fieldset"><FormLabel component="legend">Equal Variances</FormLabel><RadioGroup value={equalVariances} onChange={(e) => setEqualVariances(e.target.value)}><FormControlLabel value="auto" control={<Radio size="small" />} label="Auto-detect homogeneity (using Levene's Test)"/><FormControlLabel value="assumed" control={<Radio size="small" />} label="Assume equal variances (Student's t-test)"/><FormControlLabel value="not-assumed" control={<Radio size="small" />} label="Do not assume equal variances (Welch's t-test)"/></RadioGroup></FormControl></Grid><Grid item xs={12}><Divider sx={{ my: 1 }}/></Grid><Grid item xs={12}><FormControl component="fieldset"><FormLabel component="legend">Alternative Hypothesis</FormLabel><RadioGroup value={alternativeHypothesis} onChange={(e) => setAlternativeHypothesis(e.target.value)} row><FormControlLabel value="two-sided" control={<Radio size="small" />} label="Two-sided (μ1 ≠ μ2)"/><FormControlLabel value="less" control={<Radio size="small" />} label="One-sided (μ1 < μ2)"/><FormControlLabel value="greater" control={<Radio size="small" />} label="One-sided (μ1 > μ2)"/></RadioGroup></FormControl></Grid></Grid>), validation: validateOptionsStep },
    { label: 'Run Analysis', description: 'Review your selections and run the analysis', content: (<Box><Paper sx={{
      p: 2,
      mb: 3,
      backgroundColor: theme.palette.mode === 'dark'
        ? alpha(theme.palette.background.default, 0.8)
        : alpha(theme.palette.background.default, 0.5),
      color: theme.palette.text.primary
    }}><Typography variant="subtitle1" gutterBottom>Analysis Summary</Typography><Grid container spacing={2}><Grid item xs={12} sm={6}><Typography variant="body2"><strong>Test Type:</strong> Independent Samples t-Test</Typography><Typography variant="body2"><strong>Dataset:</strong> {dataset?.name || 'None selected'}</Typography><Typography variant="body2"><strong>Dependent Variable:</strong> {dataset?.columns.find(c=>c.id === dependentVariable)?.name || 'N/A'}</Typography></Grid><Grid item xs={12} sm={6}><Typography variant="body2"><strong>Grouping Variable:</strong> {dataset?.columns.find(c=>c.id === groupingVariable)?.name || 'N/A'}</Typography><Typography variant="body2"><strong>Groups:</strong> {groupValues.join(', ') || 'N/A'}</Typography><Typography variant="body2"><strong>Significance Level:</strong> {significanceLevel}</Typography></Grid></Grid></Paper>{error && (<Alert severity="error" sx={{ mb: 3 }}><AlertTitle>Error</AlertTitle>{error}</Alert>)}<Box sx={{ textAlign: 'center' }}><Button variant="contained" color="primary" size="large" startIcon={<PlayArrowIcon />} onClick={runAnalysis} disabled={!validateDatasetStep() || !validateVariablesStep() || !validateOptionsStep()}>Run t-Test</Button></Box></Box>) }
  ];
  
  return (
    <Box sx={{ width: '100%', minHeight: '600px' }}>
      <Typography variant="h6" gutterBottom>Independent Samples t-Test</Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Compare the means of a continuous variable between two independent groups.
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} lg={showResults ? 4 : 12}>
          <AnalysisSteps 
            steps={analysisSteps} 
            variant="outlined"
            onComplete={runAnalysis}
            finalActionLabel="Run t-Test"
            finalActionIcon={<PlayArrowIcon />}
          />
        </Grid>
        
        {showResults && results && (
          <Grid item xs={12} lg={8}>
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{mb: 2}}>Assumptions Check</Typography>
              
              <Box mb={2}>
                <Typography variant="subtitle1" gutterBottom>1. Sample Size</Typography>
                <Typography variant="body2">Group {results.group1.name}: N = {results.group1.n}</Typography>
                <Typography variant="body2">Group {results.group2.name}: N = {results.group2.n}</Typography>
                {(results.group1.n < 2 || results.group2.n < 2) ? (
                  <Alert severity="error" sx={{ mt: 1 }}>One or both groups have insufficient data (N &lt; 2).</Alert>
                ) : (results.group1.n < 30 || results.group2.n < 30) ? (
                  <Alert severity="info" sx={{ mt: 1 }}>Small sample size(s). T-test results may be less robust, especially if other assumptions are violated.</Alert>
                ) : (
                  <Alert severity="success" sx={{ mt: 1 }}>Sample sizes are adequate.</Alert>
                )}
              </Box>
              <Divider sx={{ my: 2 }} />

              <Box mb={2}>
                <Typography variant="subtitle1" gutterBottom>2. Normality Assumption (Shapiro-Wilk)</Typography>
                {results.normalityCheck && (
                  <>
                    <TableContainer component={Paper} variant="outlined" sx={{maxWidth: 400, mb:1}}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell sx={getHeaderCellSx()}>Group</TableCell>
                            <TableCell align="right" sx={getHeaderCellSx()}>p-value</TableCell>
                            <TableCell sx={getHeaderCellSx()}>Normal?</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          <TableRow>
                            <TableCell>{results.group1.name}</TableCell>
                            <TableCell align="right">{results.normalityCheck.group1.error ? 'N/A' : results.normalityCheck.group1.pValue?.toFixed(3)}</TableCell>
                            <TableCell sx={{color: results.normalityCheck.group1.error ? 'text.secondary' : results.normalityCheck.group1.isNormal ? 'success.main' : 'error.main'}}>
                              {results.normalityCheck.group1.error || (results.normalityCheck.group1.isNormal ? 'Yes' : 'No')}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>{results.group2.name}</TableCell>
                            <TableCell align="right">{results.normalityCheck.group2.error ? 'N/A' : results.normalityCheck.group2.pValue?.toFixed(3)}</TableCell>
                            <TableCell sx={{color: results.normalityCheck.group2.error ? 'text.secondary' : results.normalityCheck.group2.isNormal ? 'success.main' : 'error.main'}}>
                             {results.normalityCheck.group2.error || (results.normalityCheck.group2.isNormal ? 'Yes' : 'No')}
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                    {results.normalityCheck.group1.error || results.normalityCheck.group2.error ?
                      <Alert severity="info" sx={{mt:1}}>Normality test requires N &ge; 3 per group.</Alert> :
                    (!results.normalityCheck.group1.isNormal || !results.normalityCheck.group2.isNormal) && (results.group1.n < 30 || results.group2.n < 30) ?
                      <Alert severity="warning" sx={{mt:1}}>Normality violated with small sample(s). Consider Mann-Whitney U test.</Alert> :
                    (!results.normalityCheck.group1.isNormal || !results.normalityCheck.group2.isNormal) ?
                      <Alert severity="info" sx={{mt:1}}>Normality violated, but t-test is relatively robust with N &ge; 30 per group.</Alert> :
                      <Alert severity="success" sx={{mt:1}}>Normality assumption met for both groups.</Alert>
                    }
                  </>
                )}
              </Box>
              <Divider sx={{ my: 2 }} />

              <Box mb={2}>
                <Typography variant="subtitle1" gutterBottom>3. Equal Variances Assumption</Typography>
                {results.levenePerformed && !results.levenePerformed.error && results.levenePerformed.df && (
                  <>
                    <TableContainer component={Paper} variant="outlined" sx={{maxWidth: 500, mb:1}}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell sx={getHeaderCellSx()}>Test</TableCell>
                            <TableCell align="right" sx={getHeaderCellSx()}>F</TableCell>
                            <TableCell align="right" sx={getHeaderCellSx()}>df1</TableCell>
                            <TableCell align="right" sx={getHeaderCellSx()}>df2</TableCell>
                            <TableCell align="right" sx={getHeaderCellSx()}>p-value</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          <TableRow>
                            <TableCell>Levene's Test</TableCell>
                            <TableCell align="right">{results.levenePerformed.statistic.toFixed(3)}</TableCell>
                            <TableCell align="right">{results.levenePerformed.df[0]}</TableCell>
                            <TableCell align="right">{results.levenePerformed.df[1]}</TableCell>
                            <TableCell align="right">{results.levenePerformed.pValue.toFixed(3)}</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                    <Alert severity={!results.levenePerformed.rejected ? "success" : "warning"} sx={{ mt: 1 }}>
                      {!results.levenePerformed.rejected
                        ? `Equal variances met (p > ${results.levenePerformed.alpha?.toFixed(2)}). ${results.testType} is appropriate.`
                        : `Equal variances NOT met (p <= ${results.levenePerformed.alpha?.toFixed(2)}). ${results.testType} was used.`}
                    </Alert>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontSize: '0.875rem' }}>
                      <strong>Note:</strong> Levene's test uses the median-based variant (Brown-Forsythe), which is more robust to non-normal distributions.
                    </Typography>
                  </>
                )}
                {results.levenePerformed && results.levenePerformed.error && (
                  <Alert severity="warning">Levene's Test: {results.levenePerformed.error}. {results.testType} used.</Alert>
                )}
                {!results.levenePerformed && (
                   <Alert severity="info">User selected: {results.assumedEqualVariancesFinal ? 'Assume equal variances' : 'Do not assume equal variances'}. {results.testType} performed.</Alert>
                )}
              </Box>
            </Paper>
            
            <Fade in={showResults}>
              <Box>
                <AnalysisResultCard
                  title={`Detailed t-Test Results: ${results.dependentVariable} by ${results.groupingVariable}`}
                  timestamp={results.timestamp}
                  description={`Comparing ${results.dependentVariable} between ${results.group1.name} and ${results.group2.name} groups. Test performed: ${results.testType}.`}
                  pValue={results.pValue}
                  significance={results.significanceLevel}
                  statisticalTests={[
                    { name: 't-value', value: results.t, description: 'The t-statistic measures the size of the difference relative to the variation in your sample data' },
                    { name: 'degrees of freedom', value: results.df },
                    { name: 'p-value', value: results.pValue, pValue: results.pValue, significant: results.pValue <= results.significanceLevel },
                    { name: "Cohen's d", value: results.cohensD, description: 'Effect size, values of 0.2, 0.5, and 0.8 indicate small, medium, and large effects' }
                  ]}
                  chart={renderResultsChart()}
                  tableData={{
                    columns: ['Group', 'N', 'Mean', 'SD', 'SE Mean'],
                    rows: [
                      [ results.group1.name, results.group1.n, results.group1.mean.toFixed(4), results.group1.sd.toFixed(4), (results.group1.sd / Math.sqrt(results.group1.n)).toFixed(4) ],
                      [ results.group2.name, results.group2.n, results.group2.mean.toFixed(4), results.group2.sd.toFixed(4), (results.group2.sd / Math.sqrt(results.group2.n)).toFixed(4) ]
                    ]
                  }}
                  interpretations={[
                    results.pValue <= results.significanceLevel
                      ? `There is a statistically significant difference in ${results.dependentVariable} between ${results.group1.name} (M = ${results.group1.mean.toFixed(2)}, SD = ${results.group1.sd.toFixed(2)}) and ${results.group2.name} (M = ${results.group2.mean.toFixed(2)}, SD = ${results.group2.sd.toFixed(2)}); t(${results.df.toFixed(2)}) = ${results.t.toFixed(2)}, p = ${results.pValue < 0.001 ? '< 0.001' : results.pValue.toFixed(3)}.`
                      : `There is no statistically significant difference in ${results.dependentVariable} between ${results.group1.name} (M = ${results.group1.mean.toFixed(2)}, SD = ${results.group1.sd.toFixed(2)}) and ${results.group2.name} (M = ${results.group2.mean.toFixed(2)}, SD = ${results.group2.sd.toFixed(2)}); t(${results.df.toFixed(2)}) = ${results.t.toFixed(2)}, p = ${results.pValue.toFixed(3)}.`,
                    `The effect size (Cohen's d = ${results.cohensD.toFixed(2)}) indicates a ${ results.cohensD < 0.2 ? 'negligible' : results.cohensD < 0.5 ? 'small' : results.cohensD < 0.8 ? 'medium' : 'large'} practical significance.`
                  ]}
                  assumptions={[ 
                    { name: 'Independence of observations', status: 'passed', message: 'Assumed based on study design' },
                    { name: 'Normality (Overall)', status: (results.normalityCheck?.group1.isNormal && results.normalityCheck?.group2.isNormal) ? 'passed' : (results.normalityCheck?.group1.error || results.normalityCheck?.group2.error) ? 'warning' : 'failed', message: 'Detailed normality check per group provided above.' },
                    { name: 'Homogeneity of Variances (Overall)', status: results.levenePerformed && results.levenePerformed.error ? 'warning' : results.assumedEqualVariancesFinal ? 'passed' : 'failed', message: 'Detailed Levene\'s test results or user selection noted above.'}
                  ]}
                  footnotes={[
                    `${results.testType} performed. Alternative hypothesis: ${results.alternativeHypothesis}. Significance level (α): ${results.significanceLevel}.`,
                    `Normality checks (Shapiro-Wilk) performed for each group. See details above.`,
                    results.levenePerformed && !results.levenePerformed.error ? `Levene's test performed for homogeneity of variances (p=${results.levenePerformed.pValue.toFixed(3)}). See details above.` : results.levenePerformed && results.levenePerformed.error ? `Levene's test could not be performed. Welch's t-test used.` : `Homogeneity of variances based on user selection.`
                  ]}
                  onSave={() => console.log('Saving results...')}
                  onCopy={() => console.log('Copying results...')}
                />
              </Box>
            </Fade>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default IndependentTTest;
