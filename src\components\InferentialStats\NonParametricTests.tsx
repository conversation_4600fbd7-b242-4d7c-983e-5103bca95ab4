import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  alpha,

  Chip, // Added for Friedman variables
  Checkbox, // Added for Friedman variables
  ListItemText // Added for Friedman variables
} from '@mui/material';
import {
  Science as ScienceIcon,
  InfoOutlined as InfoIcon // Added for Friedman results display
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import { useTableTheme } from '../../hooks';
import {
  mannWhitneyUTest,
  wilcoxonSignedRankTest,
  kruskalWallisTest,
  calculateMean,
  calculateMedian,
  calculateStandardDeviation,
  createCrossTabulation,
  chiSquareTest,
  calculateFriedmanTest // Added Friedman test function
} from '@/utils/stats';
import { FriedmanTestResult } from '@/utils/stats/friedman'; // Added Friedman types
import { extractNumericValues } from '../../utils/typeConversions';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';

// Types of non-parametric tests
type TestType = 'mannWhitney' | 'wilcoxon' | 'kruskalWallis' | 'chiSquare' | 'friedman';

// Component for non-parametric tests analysis
const NonParametricTests: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  const { getHeaderCellSx } = useTableTheme();
  
  // State for test options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [testType, setTestType] = useState<TestType>('mannWhitney');
  const [selectedVariable1, setSelectedVariable1] = useState<string>('');
  const [selectedVariable2, setSelectedVariable2] = useState<string>('');
  const [selectedFactor, setSelectedFactor] = useState<string>('');
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [selectedGroupingVariable, setSelectedGroupingVariable] = useState<string>('');
  const [group1Value, setGroup1Value] = useState<string>('');
  const [group2Value, setGroup2Value] = useState<string>('');
  const [friedmanVariables, setFriedmanVariables] = useState<string[]>([]); // For Friedman Test
  
  // State for results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<any | null>(null);
  
  // Load saved results from localStorage on component mount and when test type changes
  useEffect(() => {
    const savedTestResults = localStorage.getItem(`nonparametric_${testType}_results`);
    
    if (savedTestResults) {
      try {
        setTestResults(JSON.parse(savedTestResults));
      } catch (e) {
        console.error('Error parsing saved test results:', e);
      }
    }
  }, [testType]);
  
  // Get numeric columns from current dataset
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Get categorical columns for grouping
  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN
  ) || [];
  
  // Get unique values for the selected grouping variable
  const groupingValues = (() => {
    if (!currentDataset || !selectedGroupingVariable) return [];
    
    const column = currentDataset.columns.find(col => col.id === selectedGroupingVariable);
    if (!column) return [];
    
    const values = currentDataset.data
      .map(row => String(row[column.name]))
      .filter((value, index, self) => self.indexOf(value) === index);
    
    return values;
  })();
  
  // Get unique values for the selected factor
  const factorValues = (() => {
    if (!currentDataset || !selectedFactor) return [];
    
    const column = currentDataset.columns.find(col => col.id === selectedFactor);
    if (!column) return [];
    
    const values = currentDataset.data
      .map(row => String(row[column.name]))
      .filter((value, index, self) => self.indexOf(value) === index && value !== null && value !== undefined && value !== '');
    
    return values.sort();
  })();
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    resetSelections();
    
    // Update the current dataset in the DataContext
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Reset selections
  const resetSelections = () => {
    setSelectedVariable1('');
    setSelectedVariable2('');
    setSelectedFactor('');
    setSelectedGroups([]);
    setSelectedGroupingVariable('');
    setGroup1Value('');
    setGroup2Value('');
    
    // Clear saved results for the current test type
    localStorage.removeItem(`nonparametric_${testType}_results`);
    
    setTestResults(null);
    setFriedmanVariables([]); // Reset Friedman variables
  };
  
  // Handle test type change
  const handleTestTypeChange = (event: SelectChangeEvent<TestType>) => {
    setTestType(event.target.value as TestType);
    resetSelections();
  };
  
  // Handle variable selection changes
  const handleVariable1Change = (event: SelectChangeEvent<string>) => {
    setSelectedVariable1(event.target.value);
    setTestResults(null);
  };
  
  const handleVariable2Change = (event: SelectChangeEvent<string>) => {
    setSelectedVariable2(event.target.value);
    setTestResults(null);
  };
  
  // Handle grouping variable change
  const handleGroupingVariableChange = (event: SelectChangeEvent<string>) => {
    setSelectedGroupingVariable(event.target.value);
    setGroup1Value('');
    setGroup2Value('');
    setTestResults(null);
  };
  
  // Handle factor selection change
  const handleFactorChange = (event: SelectChangeEvent<string>) => {
    setSelectedFactor(event.target.value);
    setSelectedGroups([]);
    setTestResults(null);
  };
  
  // Handle group value changes
  const handleGroup1ValueChange = (event: SelectChangeEvent<string>) => {
    setGroup1Value(event.target.value);
    setTestResults(null);
  };
  
  const handleGroup2ValueChange = (event: SelectChangeEvent<string>) => {
    setGroup2Value(event.target.value);
    setTestResults(null);
  };
  
  // Handle groups selection change
  const handleGroupsChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedGroups(typeof value === 'string' ? value.split(',') : value);
    setTestResults(null);
  };

  // Handle Friedman variables selection change
  const handleFriedmanVariablesChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setFriedmanVariables(typeof value === 'string' ? value.split(',') : value);
    setTestResults(null);
  };
  
  // Check if form is valid
  const isFormValid = () => {
    if (!currentDataset) return false;
    
    switch (testType) {
      case 'mannWhitney':
        if (selectedGroupingVariable) {
          return !!selectedVariable1 && !!group1Value && !!group2Value && group1Value !== group2Value;
        } else {
          return !!selectedVariable1 && !!selectedVariable2;
        }
      case 'wilcoxon':
        return !!selectedVariable1 && !!selectedVariable2;
      case 'kruskalWallis':
        return !!selectedVariable1 && !!selectedFactor && (selectedGroups.length >= 3 || (factorValues.length >= 3 && selectedGroups.length === 0));
      case 'chiSquare':
        return !!selectedVariable1 && !!selectedVariable2;
      case 'friedman':
        return friedmanVariables.length >= 3;
      default:
        return false;
    }
  };
  
  // Run non-parametric test
  const runTest = () => {
    if (!currentDataset || !isFormValid()) {
      setError('Please select all required variables for the selected test.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setTestResults(null);
    
    try {
      let results: any = {};
      
      // Mann-Whitney U Test
      if (testType === 'mannWhitney') {
        // Get column objects
        const variable1Column = currentDataset.columns.find(col => col.id === selectedVariable1);
        
        if (!variable1Column) {
          throw new Error('Selected variable not found in dataset.');
        }
        
        // Get data
        let group1Data: number[] = [];
        let group2Data: number[] = [];
        let groupNames: string[] = [];
        
        if (selectedGroupingVariable) {
          // Using a grouping variable to split the data
          const groupingColumn = currentDataset.columns.find(col => col.id === selectedGroupingVariable);
          
          if (!groupingColumn || !group1Value || !group2Value) {
            throw new Error('Please select both group values.');
          }
          
          // Filter data for each group
          const group1Values = currentDataset.data
            .filter(row => String(row[groupingColumn.name]) === group1Value)
            .map(row => row[variable1Column.name]);
          group1Data = extractNumericValues(group1Values);

          const group2Values = currentDataset.data
            .filter(row => String(row[groupingColumn.name]) === group2Value)
            .map(row => row[variable1Column.name]);
          group2Data = extractNumericValues(group2Values);
          
          groupNames = [`${variable1Column.name} (${group1Value})`, `${variable1Column.name} (${group2Value})`];
        } else {
          // Using two separate variables
          const variable2Column = currentDataset.columns.find(col => col.id === selectedVariable2);
          
          if (!variable2Column) {
            throw new Error('Selected second variable not found in dataset.');
          }
          
          const group1Values = currentDataset.data.map(row => row[variable1Column.name]);
          group1Data = extractNumericValues(group1Values);

          const group2Values = currentDataset.data.map(row => row[variable2Column.name]);
          group2Data = extractNumericValues(group2Values);
          
          groupNames = [variable1Column.name, variable2Column.name];
        }
        
        // Check if we have enough data
        if (group1Data.length === 0 || group2Data.length === 0) {
          throw new Error('Not enough valid data in one or both groups.');
        }
        
        // Run Mann-Whitney U test
        const mannWhitneyResults = mannWhitneyUTest(group1Data, group2Data);
        
        // Calculate effect size (r)
        const n = mannWhitneyResults.n1 + mannWhitneyResults.n2;
        const effectSize = Math.abs(mannWhitneyResults.z) / Math.sqrt(n);
        
        results = {
          test: 'Mann-Whitney U Test',
          description: 'Non-parametric test for comparing two independent samples',
          statistic: mannWhitneyResults.U,
          statName: 'U',
          z: mannWhitneyResults.z,
          pValue: mannWhitneyResults.pValue,
          effectSize,
          effectSizeName: 'r',
          groups: [
            {
              name: groupNames[0],
              n: group1Data.length,
              median: calculateMedian(group1Data),
              mean: calculateMean(group1Data),
              sd: calculateStandardDeviation(group1Data)
            },
            {
              name: groupNames[1],
              n: group2Data.length,
              median: calculateMedian(group2Data),
              mean: calculateMean(group2Data),
              sd: calculateStandardDeviation(group2Data)
            }
          ]
        };
        
        // Prepare chart data
        results.chartData = results.groups.map((group: any) => ({
          name: group.name,
          value: group.median
        }));
        
        // Get interpretation
        results.interpretation = getInterpretation(results);
      }
      // Wilcoxon Signed Rank Test
      else if (testType === 'wilcoxon') {
        // Get column objects
        const variable1Column = currentDataset.columns.find(col => col.id === selectedVariable1);
        const variable2Column = currentDataset.columns.find(col => col.id === selectedVariable2);
        
        if (!variable1Column || !variable2Column) {
          throw new Error('Selected variables not found in dataset.');
        }
        
        // Get paired data (only include rows where both variables have valid values)
        const pairedData = currentDataset.data
          .map(row => ({
            value1: row[variable1Column.name],
            value2: row[variable2Column.name]
          }))
          .filter(pair => 
            typeof pair.value1 === 'number' && !isNaN(pair.value1) &&
            typeof pair.value2 === 'number' && !isNaN(pair.value2)
          );
        
        const group1Data = pairedData.map(pair => pair.value1) as number[];
        const group2Data = pairedData.map(pair => pair.value2) as number[];
        
        // Check if we have enough data
        if (group1Data.length < 5) {
          throw new Error('Not enough valid paired data. Wilcoxon test requires at least 5 pairs.');
        }
        
        // Run Wilcoxon test
        const wilcoxonResults = wilcoxonSignedRankTest(group1Data, group2Data);
        
        // Calculate effect size (r)
        const effectSize = Math.abs(wilcoxonResults.z) / Math.sqrt(wilcoxonResults.n);
        
        results = {
          test: 'Wilcoxon Signed Rank Test',
          description: 'Non-parametric test for comparing two related samples',
          statistic: wilcoxonResults.W,
          statName: 'W',
          z: wilcoxonResults.z,
          n: wilcoxonResults.n,
          pValue: wilcoxonResults.pValue,
          effectSize,
          effectSizeName: 'r',
          groups: [
            {
              name: variable1Column.name,
              n: group1Data.length,
              median: calculateMedian(group1Data),
              mean: calculateMean(group1Data),
              sd: calculateStandardDeviation(group1Data)
            },
            {
              name: variable2Column.name,
              n: group2Data.length,
              median: calculateMedian(group2Data),
              mean: calculateMean(group2Data),
              sd: calculateStandardDeviation(group2Data)
            }
          ]
        };
        
        // Prepare chart data
        results.chartData = results.groups.map((group: any) => ({
          name: group.name,
          value: group.median
        }));
        
        // Get interpretation
        results.interpretation = getInterpretation(results);
      }
      // Kruskal-Wallis Test
      else if (testType === 'kruskalWallis') {
        // Get column objects
        const variableColumn = currentDataset.columns.find(col => col.id === selectedVariable1);
        const factorColumn = currentDataset.columns.find(col => col.id === selectedFactor);
        
        if (!variableColumn || !factorColumn) {
          throw new Error('Selected variables not found.');
        }
        
        // Get unique factor values
        const factorValuesToUse = selectedGroups.length > 0 ? selectedGroups : factorValues;
        
        if (factorValuesToUse.length < 3) {
          throw new Error('Kruskal-Wallis test requires at least 3 groups. For 2 groups, use Mann-Whitney U test instead.');
        }
        
        // Separate data into groups
        const groups: number[][] = [];
        const groupNames: string[] = [];
        
        factorValuesToUse.forEach(value => {
          const allGroupValues = currentDataset.data
            .filter(row => String(row[factorColumn.name]) === value)
            .map(row => row[variableColumn.name]);
          const groupData = extractNumericValues(allGroupValues);

          if (groupData.length > 0) {
            groups.push(groupData);
            groupNames.push(value);
          }
        });
        
        if (groups.length < 3) {
          throw new Error('Not enough groups with valid data. Kruskal-Wallis test requires at least 3 groups.');
        }
        
        // Run Kruskal-Wallis test
        const kruskalWallisResults = kruskalWallisTest(groups);
        
        // Calculate effect size (eta squared)
        const etaSquared = (kruskalWallisResults.H - (groups.length - 1)) / (kruskalWallisResults.n - groups.length);
        
        results = {
          test: 'Kruskal-Wallis Test',
          description: 'Non-parametric alternative to one-way ANOVA',
          statistic: kruskalWallisResults.H,
          statName: 'H',
          df: kruskalWallisResults.df,
          n: kruskalWallisResults.n,
          pValue: kruskalWallisResults.pValue,
          effectSize: etaSquared,
          effectSizeName: 'η²',
          groups: groupNames.map((name, index) => ({
            name,
            n: groups[index].length,
            median: calculateMedian(groups[index]),
            mean: calculateMean(groups[index]),
            sd: calculateStandardDeviation(groups[index])
          }))
        };
        
        // Prepare chart data
        results.chartData = results.groups.map((group: { name: string; median: number }) => ({
          name: group.name,
          value: group.median
        }));
        
        // Get interpretation
        results.interpretation = getInterpretation(results);
      }
      // Friedman Test
      else if (testType === 'friedman') {
        if (friedmanVariables.length < 3) {
          throw new Error('Friedman test requires at least 3 numeric/ordinal variables.');
        }
        if (!currentDataset) throw new Error('Dataset not available'); // Should be caught by isFormValid

        const dataForFriedman: number[][] = [];
        const n = currentDataset.data.length;

        for (let i = 0; i < n; i++) {
          const subjectData: number[] = [];
          for (const varId of friedmanVariables) {
            const column = currentDataset.columns.find(col => col.id === varId);
            if (!column) throw new Error(`Column with id ${varId} not found.`);
            const value = currentDataset.data[i][column.name];
            if (typeof value !== 'number' || isNaN(value)) {
              // Attempt to parse if it's a string representation of a number
              const parsedValue = parseFloat(String(value));
              if (isNaN(parsedValue)) {
                throw new Error(`Invalid data for subject ${i + 1}, variable ${column.name}. Expected a number, got ${value}.`);
              }
              subjectData.push(parsedValue);
            } else {
              subjectData.push(value);
            }
          }
          dataForFriedman.push(subjectData);
        }
        results = calculateFriedmanTest(dataForFriedman) as FriedmanTestResult;
        results.summary = `Friedman Test for repeated measures on variables: ${friedmanVariables.map(id => currentDataset.columns.find(c=>c.id===id)?.name || id).join(', ')}`;
        results.test = 'Friedman Test'; // Ensure test name is set for interpretation
      }
      // Chi-Square Test
      else if (testType === 'chiSquare') {
        // Get column objects
        const variable1Column = currentDataset.columns.find(col => col.id === selectedVariable1);
        const variable2Column = currentDataset.columns.find(col => col.id === selectedVariable2);
        
        if (!variable1Column || !variable2Column) {
          throw new Error('Selected variables not found in dataset.');
        }
        
        // Get column data
        const var1Values = currentDataset.data.map(row => String(row[variable1Column.name] ?? 'Missing'));
        const var2Values = currentDataset.data.map(row => String(row[variable2Column.name] ?? 'Missing'));
        
        // Create cross-tabulation
        const crossTab = createCrossTabulation(var1Values, var2Values);
        
        // Get unique categories
        const rowCategories = Object.keys(crossTab);
        const columnCategories = Object.keys(rowCategories.length > 0 ? crossTab[rowCategories[0]] : {});
        
        if (rowCategories.length < 2 || columnCategories.length < 2) {
          throw new Error('Chi-Square test requires at least 2 categories for each variable.');
        }
        
        // Convert cross-tab to format needed for chi-square
        const contingencyTable = rowCategories.map(row => 
          columnCategories.map(col => crossTab[row][col] || 0)
        );
        
        // Run chi-square test
        const chiSquareResults = chiSquareTest(contingencyTable);
        
        // Calculate row and column totals
        const rowTotals: Record<string, number> = {};
        const columnTotals: Record<string, number> = {};
        let grandTotal = 0;
        
        rowCategories.forEach(row => {
          rowTotals[row] = 0;
          columnCategories.forEach(col => {
            const count = crossTab[row][col] || 0;
            rowTotals[row] += count;
            columnTotals[col] = (columnTotals[col] || 0) + count;
            grandTotal += count;
          });
        });
        
        results = {
          test: 'Chi-Square Test of Independence',
          description: 'Test association between categorical variables',
          statistic: chiSquareResults.chiSquare,
          statName: 'χ²',
          df: chiSquareResults.df,
          pValue: chiSquareResults.pValue,
          effectSize: chiSquareResults.cramersV,
          effectSizeName: 'Cramer\'s V',
          contingencyTable: {
            rowCategories,
            columnCategories,
            frequencies: crossTab,
            rowTotals,
            columnTotals,
            grandTotal
          },
          variables: [
            { name: variable1Column.name, categories: rowCategories },
            { name: variable2Column.name, categories: columnCategories }
          ]
        };
        
        // Prepare chart data for stacked bar chart
        results.chartData = rowCategories.map(row => {
          const dataPoint: { [key: string]: string | number } = { category: row };
          columnCategories.forEach(col => {
            dataPoint[col] = crossTab[row][col] || 0;
          });
          return dataPoint;
        });
        
        // Get interpretation
        results.interpretation = getInterpretation(results);
      }
      
      setTestResults(results);
      
      // Save test results to localStorage
      localStorage.setItem(`nonparametric_${testType}_results`, JSON.stringify(results));
      
      setLoading(false);
    } catch (err) {
      setError(`Error running test: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };
  
  // Interpret effect size
  const interpretEffectSize = (value: number, type: string) => {
    if (type === 'r') {
      // r effect size for Mann-Whitney and Wilcoxon
      if (value < 0.1) return 'Very Small';
      if (value < 0.3) return 'Small';
      if (value < 0.5) return 'Medium';
      return 'Large';
    } else if (type === 'η²') {
      // Eta squared for Kruskal-Wallis
      if (value < 0.01) return 'Very Small';
      if (value < 0.06) return 'Small';
      if (value < 0.14) return 'Medium';
      return 'Large';
    } else if (type === 'Cramer\'s V') {
      // Cramer's V for Chi-Square
      if (value < 0.1) return 'Very Small';
      if (value < 0.3) return 'Small';
      if (value < 0.5) return 'Medium';
      return 'Large';
    }
    return 'Unknown';
  };
  
  // Get test interpretation
  const getInterpretation = (results: any) => {
    if (!results) return '';
    
    const { test, pValue, statistic, statName, effectSize, effectSizeName, groups, variables } = results;
    
    let interpretation = '';
    
    // Determine significance based on p-value
    const isSignificant = pValue < 0.05;
    
    // Interpret based on test type
    if (test === 'Mann-Whitney U Test') {
      interpretation = `A ${test} was conducted to compare ${groups[0].name} (Median = ${groups[0].median.toFixed(2)}) and ${groups[1].name} (Median = ${groups[1].median.toFixed(2)}).`;
      
      if (isSignificant) {
        interpretation += ` There was a statistically significant difference between the groups (${statName} = ${statistic.toFixed(2)}, z = ${results.z.toFixed(2)}, p = ${pValue < 0.001 ? '< 0.001' : pValue.toFixed(3)}).`;
      } else {
        interpretation += ` There was no statistically significant difference between the groups (${statName} = ${statistic.toFixed(2)}, z = ${results.z.toFixed(2)}, p = ${pValue.toFixed(3)}).`;
      }
      
      // Add effect size interpretation
      const effectSizeMagnitude = interpretEffectSize(effectSize, effectSizeName);
      interpretation += ` The effect size (${effectSizeName} = ${effectSize.toFixed(3)}) indicates a ${effectSizeMagnitude.toLowerCase()} effect.`;
    } else if (test === 'Friedman Test') {
      const friedmanResult = results as FriedmanTestResult;
      interpretation = `A ${test} was conducted to compare the ranks of ${friedmanResult.k} related conditions/variables for ${friedmanResult.n} subjects.`;
      if (isSignificant) {
        interpretation += ` There was a statistically significant difference among the mean ranks of the conditions/variables (χ²(${friedmanResult.df}) = ${friedmanResult.statistic?.toFixed(2)}, p = ${pValue < 0.001 ? '< 0.001' : pValue?.toFixed(3)}).`;
        interpretation += ' Post-hoc tests (e.g., Wilcoxon signed-rank tests with Bonferroni correction) may be needed to identify which specific pairs of conditions/variables differ.';
      } else {
        interpretation += ` There was no statistically significant difference among the mean ranks of the conditions/variables (χ²(${friedmanResult.df}) = ${friedmanResult.statistic?.toFixed(2)}, p = ${pValue?.toFixed(3)}).`;
      }
      // Note: Standard effect size for Friedman is Kendall's W, not directly calculated here yet.
      // For now, we'll skip specific effect size magnitude for Friedman in this generic function.
    } else if (test === 'Wilcoxon Signed Rank Test') {
      interpretation = `A ${test} was conducted to compare ${groups[0].name} (Median = ${groups[0].median.toFixed(2)}) and ${groups[1].name} (Median = ${groups[1].median.toFixed(2)}) in a within-subjects design with ${results.n} paired observations.`;
      
      if (isSignificant) {
        interpretation += ` There was a statistically significant difference between the paired measurements (${statName} = ${statistic.toFixed(2)}, z = ${results.z.toFixed(2)}, p = ${pValue < 0.001 ? '< 0.001' : pValue.toFixed(3)}).`;
      } else {
        interpretation += ` There was no statistically significant difference between the paired measurements (${statName} = ${statistic.toFixed(2)}, z = ${results.z.toFixed(2)}, p = ${pValue.toFixed(3)}).`;
      }
      
      // Add effect size interpretation
      const effectSizeMagnitude = interpretEffectSize(effectSize, effectSizeName);
      interpretation += ` The effect size (${effectSizeName} = ${effectSize.toFixed(3)}) indicates a ${effectSizeMagnitude.toLowerCase()} effect.`;
    } else if (test === 'Friedman Test') {
      const friedmanResult = results as FriedmanTestResult;
      interpretation = `A ${test} was conducted to compare the ranks of ${friedmanResult.k} related conditions/variables for ${friedmanResult.n} subjects.`;
      if (isSignificant) {
        interpretation += ` There was a statistically significant difference among the mean ranks of the conditions/variables (χ²(${friedmanResult.df}) = ${friedmanResult.statistic?.toFixed(2)}, p = ${pValue < 0.001 ? '< 0.001' : pValue?.toFixed(3)}).`;
        interpretation += ' Post-hoc tests (e.g., Wilcoxon signed-rank tests with Bonferroni correction) may be needed to identify which specific pairs of conditions/variables differ.';
      } else {
        interpretation += ` There was no statistically significant difference among the mean ranks of the conditions/variables (χ²(${friedmanResult.df}) = ${friedmanResult.statistic?.toFixed(2)}, p = ${pValue?.toFixed(3)}).`;
      }
      // Note: Standard effect size for Friedman is Kendall's W, not directly calculated here yet.
      // For now, we'll skip specific effect size magnitude for Friedman in this generic function.
    } else if (test === 'Kruskal-Wallis Test') {
      interpretation = `A ${test} was conducted to compare the effect of ${groups.length} groups (${groups.map((g: { name: string }) => g.name).join(', ')}) on the dependent variable.`;
      
      if (isSignificant) {
        interpretation += ` There was a statistically significant difference between the groups (${statName} = ${statistic.toFixed(2)}, df = ${results.df}, p = ${pValue < 0.001 ? '< 0.001' : pValue.toFixed(3)}).`;
        
        // Add comparison of medians
        const medians = groups.map((g: { name: string; median: number }) => `${g.name} (Median = ${g.median.toFixed(2)})`).join(', ');
        interpretation += ` The medians were: ${medians}.`;
        
        // Note about post-hoc tests
        interpretation += ` Post-hoc pairwise comparisons would be needed to determine which specific groups differ from each other.`;
      } else {
        interpretation += ` There was no statistically significant difference between the groups (${statName} = ${statistic.toFixed(2)}, df = ${results.df}, p = ${pValue.toFixed(3)}).`;
      }
      
      // Add effect size interpretation
      const effectSizeMagnitude = interpretEffectSize(effectSize, effectSizeName);
      interpretation += ` The effect size (${effectSizeName} = ${effectSize.toFixed(3)}) indicates a ${effectSizeMagnitude.toLowerCase()} effect.`;
    } else if (test === 'Friedman Test') {
      const friedmanResult = results as FriedmanTestResult;
      interpretation = `A ${test} was conducted to compare the ranks of ${friedmanResult.k} related conditions/variables for ${friedmanResult.n} subjects.`;
      if (isSignificant) {
        interpretation += ` There was a statistically significant difference among the mean ranks of the conditions/variables (χ²(${friedmanResult.df}) = ${friedmanResult.statistic?.toFixed(2)}, p = ${pValue < 0.001 ? '< 0.001' : pValue?.toFixed(3)}).`;
        interpretation += ' Post-hoc tests (e.g., Wilcoxon signed-rank tests with Bonferroni correction) may be needed to identify which specific pairs of conditions/variables differ.';
      } else {
        interpretation += ` There was no statistically significant difference among the mean ranks of the conditions/variables (χ²(${friedmanResult.df}) = ${friedmanResult.statistic?.toFixed(2)}, p = ${pValue?.toFixed(3)}).`;
      }
      // Note: Standard effect size for Friedman is Kendall's W, not directly calculated here yet.
      // For now, we'll skip specific effect size magnitude for Friedman in this generic function.
    } else if (test === 'Chi-Square Test of Independence') {
      interpretation = `A ${test} was conducted to examine the relationship between ${variables[0].name} and ${variables[1].name}.`;
      
      if (isSignificant) {
        interpretation += ` There was a statistically significant association between the variables (${statName} = ${statistic.toFixed(2)}, df = ${results.df}, p = ${pValue < 0.001 ? '< 0.001' : pValue.toFixed(3)}).`;
        interpretation += ` This suggests that the distribution of ${variables[1].name} differs depending on the category of ${variables[0].name}.`;
      } else {
        interpretation += ` There was no statistically significant association between the variables (${statName} = ${statistic.toFixed(2)}, df = ${results.df}, p = ${pValue.toFixed(3)}).`;
        interpretation += ` This suggests that the distribution of ${variables[1].name} does not differ significantly based on the category of ${variables[0].name}.`;
      }
      
      // Add effect size interpretation
      const effectSizeMagnitude = interpretEffectSize(effectSize, effectSizeName);
      interpretation += ` The effect size (${effectSizeName} = ${effectSize.toFixed(3)}) indicates a ${effectSizeMagnitude.toLowerCase()} effect.`;
    } else if (test === 'Friedman Test') {
      const friedmanResult = results as FriedmanTestResult;
      interpretation = `A ${test} was conducted to compare the ranks of ${friedmanResult.k} related conditions/variables for ${friedmanResult.n} subjects.`;
      if (isSignificant) {
        interpretation += ` There was a statistically significant difference among the mean ranks of the conditions/variables (χ²(${friedmanResult.df}) = ${friedmanResult.statistic?.toFixed(2)}, p = ${pValue < 0.001 ? '< 0.001' : pValue?.toFixed(3)}).`;
        interpretation += ' Post-hoc tests (e.g., Wilcoxon signed-rank tests with Bonferroni correction) may be needed to identify which specific pairs of conditions/variables differ.';
      } else {
        interpretation += ` There was no statistically significant difference among the mean ranks of the conditions/variables (χ²(${friedmanResult.df}) = ${friedmanResult.statistic?.toFixed(2)}, p = ${pValue?.toFixed(3)}).`;
      }
      // Note: Standard effect size for Friedman is Kendall's W, not directly calculated here yet.
      // For now, we'll skip specific effect size magnitude for Friedman in this generic function.
    }
    
    return interpretation;
  };
  
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Non-Parametric Tests
      </Typography>
      
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Test Selection
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="test-type-label">Test Type</InputLabel>
              <Select
                labelId="test-type-label"
                id="test-type"
                value={testType}
                label="Test Type"
                onChange={handleTestTypeChange}
              >
                <MenuItem value="mannWhitney">
                  Mann-Whitney U Test
                  <Typography variant="caption" display="block" color="text.secondary">
                    Compare two independent groups (non-parametric alternative to t-test)
                  </Typography>
                </MenuItem>
                <MenuItem value="wilcoxon">
                  Wilcoxon Signed-Rank Test
                  <Typography variant="caption" display="block" color="text.secondary">
                    Compare two related samples (non-parametric alternative to paired t-test)
                  </Typography>
                </MenuItem>
                <MenuItem value="kruskalWallis">
                  Kruskal-Wallis Test
                  <Typography variant="caption" display="block" color="text.secondary">
                    Compare three or more independent groups (non-parametric alternative to ANOVA)
                  </Typography>
                </MenuItem>
                <MenuItem value="chiSquare">
                  Chi-Square Test
                  <Typography variant="caption" display="block" color="text.secondary">
                    Test association between categorical variables
                  </Typography>
                </MenuItem>
                <MenuItem value="friedman">
                  Friedman Test
                  <Typography variant="caption" display="block" color="text.secondary">
                    Compare three or more related groups (non-parametric Repeated Measures ANOVA)
                  </Typography>
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>
      
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Variable Selection
        </Typography>
        
        {/* Mann-Whitney U Test */}
        {testType === 'mannWhitney' && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="variable1-label">Variable</InputLabel>
                <Select
                  labelId="variable1-label"
                  id="variable1"
                  value={selectedVariable1}
                  label="Variable"
                  onChange={handleVariable1Change}
                  disabled={numericColumns.length === 0}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    numericColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="grouping-label">Grouping Variable</InputLabel>
                <Select
                  labelId="grouping-label"
                  id="grouping"
                  value={selectedGroupingVariable}
                  label="Grouping Variable"
                  onChange={handleGroupingVariableChange}
                  disabled={categoricalColumns.length === 0}
                >
                  <MenuItem value="">
                    <em>None (use two separate variables)</em>
                  </MenuItem>
                  {categoricalColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {selectedGroupingVariable ? (
              // If using a grouping variable, show group value selectors
              <>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="group1-label">Group 1</InputLabel>
                    <Select
                      labelId="group1-label"
                      id="group1"
                      value={group1Value}
                      label="Group 1"
                      onChange={handleGroup1ValueChange}
                      disabled={groupingValues.length === 0}
                    >
                      {groupingValues.map(value => (
                        <MenuItem key={value} value={value}>
                          {value}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="group2-label">Group 2</InputLabel>
                    <Select
                      labelId="group2-label"
                      id="group2"
                      value={group2Value}
                      label="Group 2"
                      onChange={handleGroup2ValueChange}
                      disabled={groupingValues.length === 0}
                    >
                      {groupingValues.map(value => (
                        <MenuItem key={value} value={value}>
                          {value}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </>
            ) : (
              // If not using a grouping variable, show a second variable selector
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="variable2-label">Second Variable</InputLabel>
                  <Select
                    labelId="variable2-label"
                    id="variable2"
                    value={selectedVariable2}
                    label="Second Variable"
                    onChange={handleVariable2Change}
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>
                        No numeric variables available
                      </MenuItem>
                    ) : (
                      numericColumns.map(column => (
                        <MenuItem key={column.id} value={column.id}>
                          {column.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>
            )}
          </Grid>
        )}

        
        {/* Wilcoxon Signed-Rank Test */}
        {testType === 'wilcoxon' && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="variable1-label">First Variable</InputLabel>
                <Select
                  labelId="variable1-label"
                  id="variable1"
                  value={selectedVariable1}
                  label="First Variable"
                  onChange={handleVariable1Change}
                  disabled={numericColumns.length === 0}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    numericColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="variable2-label">Second Variable</InputLabel>
                <Select
                  labelId="variable2-label"
                  id="variable2"
                  value={selectedVariable2}
                  label="Second Variable"
                  onChange={handleVariable2Change}
                  disabled={numericColumns.length === 0}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    numericColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
              
              <Typography variant="caption" color="text.secondary">
                Note: Wilcoxon test requires paired observations. Values from both variables will be paired by row.
              </Typography>
            </Grid>
          </Grid>
        )}
        {/* Chi-Square Test */}
        {testType === 'chiSquare' && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="variable1-label">First Categorical Variable</InputLabel>
                <Select
                  labelId="variable1-label"
                  id="variable1"
                  value={selectedVariable1}
                  label="First Categorical Variable"
                  onChange={handleVariable1Change}
                  disabled={categoricalColumns.length === 0}
                >
                  {categoricalColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No categorical variables available
                    </MenuItem>
                  ) : (
                    categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="variable2-label">Second Categorical Variable</InputLabel>
                <Select
                  labelId="variable2-label"
                  id="variable2"
                  value={selectedVariable2}
                  label="Second Categorical Variable"
                  onChange={handleVariable2Change}
                  disabled={categoricalColumns.length === 0}
                >
                  {categoricalColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No categorical variables available
                    </MenuItem>
                  ) : (
                    categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
              
              <Typography variant="caption" color="text.secondary">
                Note: Chi-Square test requires categorical variables with at least 2 categories each.
              </Typography>
            </Grid>
          </Grid>
        )}
        {/* Kruskal-Wallis Test */}
        {testType === 'kruskalWallis' && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="variable1-label">Dependent Variable (Numeric)</InputLabel>
                <Select
                  labelId="variable1-label"
                  id="variable1"
                  value={selectedVariable1}
                  label="Dependent Variable (Numeric)"
                  onChange={handleVariable1Change}
                  disabled={numericColumns.length === 0}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    numericColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="factor-label">Factor (Categorical)</InputLabel>
                <Select
                  labelId="factor-label"
                  id="factor"
                  value={selectedFactor}
                  label="Factor (Categorical)"
                  onChange={handleFactorChange}
                  disabled={categoricalColumns.length === 0}
                >
                  {categoricalColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No categorical variables available
                    </MenuItem>
                  ) : (
                    categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
            
            {selectedFactor && factorValues.length > 0 && (
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="groups-label">Groups to Include (Optional)</InputLabel>
                  <Select
                    labelId="groups-label"
                    id="groups"
                    multiple
                    value={selectedGroups}
                    label="Groups to Include (Optional)"
                    onChange={handleGroupsChange}
                    renderValue={(selected) => selected.join(', ')}
                  >
                    {factorValues.map((value) => (
                      <MenuItem key={value} value={value}>
                        {value}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Typography variant="caption" color="text.secondary">
                  If no groups are selected, all groups will be included. Kruskal-Wallis test requires at least 3 groups.
                </Typography>
              </Grid>
            )}
          </Grid>
        )}

        {/* Friedman Test */}
        {testType === 'friedman' && (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="friedman-variables-label">Repeated Measures Variables (≥3)</InputLabel>
                <Select
                  labelId="friedman-variables-label"
                  id="friedman-variables"
                  multiple
                  value={friedmanVariables}
                  onChange={handleFriedmanVariablesChange}
                  label="Repeated Measures Variables (≥3)"
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map((value) => {
                        const columnName = numericColumns.find(col => col.id === value)?.name || value;
                        return <Chip key={value} label={columnName} />;
                      })}
                    </Box>
                  )}
                  disabled={numericColumns.length < 3}
                >
                  {numericColumns.map(col => (
                    <MenuItem key={col.id} value={col.id}>
                      <Checkbox checked={friedmanVariables.indexOf(col.id) > -1} />
                      <ListItemText primary={col.name} />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Typography variant="caption" color="text.secondary">
                Friedman test requires at least 3 numeric variables representing repeated measurements on the same subjects.
              </Typography>
            </Grid>
          </Grid>
        )}

        
        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ScienceIcon />}
            onClick={runTest}
            disabled={loading || !isFormValid()}
          >
            Run {testType === 'mannWhitney' ? 'Mann-Whitney U Test' : 
                 testType === 'wilcoxon' ? 'Wilcoxon Signed-Rank Test' : 
                 testType === 'kruskalWallis' ? 'Kruskal-Wallis Test' : 
                 testType === 'chiSquare' ? 'Chi-Square Test' : 
                 testType === 'friedman' ? 'Friedman Test' : 'Test'}
          </Button>
        </Box>
      </Paper>
      
      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {testResults && !loading && testType !== 'friedman' && (
        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {testResults.test} Results
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {testResults.description}
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Test Statistics
              </Typography>
              
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Statistic</TableCell>
                      <TableCell align="right">Value</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell>{testResults.statName}-statistic</TableCell>
                      <TableCell align="right">{testResults.statistic.toFixed(4)}</TableCell>
                    </TableRow>
                    {testResults.z !== undefined && (
                      <TableRow>
                        <TableCell>z-score</TableCell>
                        <TableCell align="right">{testResults.z.toFixed(4)}</TableCell>
                      </TableRow>
                    )}
                    {testResults.df !== undefined && (
                      <TableRow>
                        <TableCell>Degrees of Freedom</TableCell>
                        <TableCell align="right">{testResults.df}</TableCell>
                      </TableRow>
                    )}
                    <TableRow>
                      <TableCell>p-value</TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ 
                          color: testResults.pValue < 0.05 ? 'success.main' : 'inherit',
                          fontWeight: testResults.pValue < 0.05 ? 'bold' : 'normal'
                        }}
                      >
                        {testResults.pValue < 0.001 ? '< 0.001' : testResults.pValue.toFixed(4)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Effect Size ({testResults.effectSizeName})</TableCell>
                      <TableCell align="right">
                        {testResults.effectSize.toFixed(4)}
                        <Typography variant="caption" display="block" color="text.secondary">
                          {interpretEffectSize(testResults.effectSize, testResults.effectSizeName)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
            
            <Grid item xs={12} md={6}>
              {testResults.test === 'Chi-Square Test of Independence' ? (
                // Chi-Square specific results
                <>
                  <Typography variant="subtitle2" gutterBottom>
                    Contingency Table
                  </Typography>
                  
                  {testResults.hasLowExpectedFrequencies && (
                    <Alert severity="warning" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        Some expected cell frequencies are less than 5, which may make the Chi-Square approximation inaccurate. Consider combining categories or using Fisher's exact test for small samples.
                      </Typography>
                    </Alert>
                  )}
                  
                  {testResults.contingencyTable && (
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>{testResults.variables[0].name} \ {testResults.variables[1].name}</TableCell>
                            {testResults.contingencyTable.columnCategories.map((col: string) => (
                              <TableCell key={col} align="right">{col}</TableCell>
                            ))}
                            <TableCell align="right">Total</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {testResults.contingencyTable.rowCategories.map((row: string) => (
                            <TableRow key={row}>
                              <TableCell>{row}</TableCell>
                              {testResults.contingencyTable.columnCategories.map((col: string) => (
                                <TableCell key={col} align="right">
                                  {testResults.contingencyTable.frequencies[row][col] || 0}
                                </TableCell>
                              ))}
                              <TableCell align="right">
                                {testResults.contingencyTable.rowTotals[row]}
                              </TableCell>
                            </TableRow>
                          ))}
                          <TableRow>
                            <TableCell><strong>Total</strong></TableCell>
                            {testResults.contingencyTable.columnCategories.map((col: string) => (
                              <TableCell key={col} align="right">
                                <strong>{testResults.contingencyTable.columnTotals[col]}</strong>
                              </TableCell>
                            ))}
                            <TableCell align="right">
                              <strong>{testResults.contingencyTable.grandTotal}</strong>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                  
                  {testResults.chartData && testResults.chartData.length > 0 && (
                    <Box height={300} mt={2}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={testResults.chartData}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 10,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="category" />
                          <YAxis />
                          <RechartsTooltip />
                          <Legend />
                          {testResults.contingencyTable && testResults.contingencyTable.columnCategories.map((category: string, index: number) => (
                            <Bar 
                              key={category}
                              dataKey={category} 
                              stackId="a"
                              fill={`hsl(${index * 137.5 % 360}, 70%, 50%)`}
                            />
                          ))}
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  )}
                </>
              ) : (
                // Standard results for other tests
                <>
                  <Typography variant="subtitle2" gutterBottom>
                    Descriptive Statistics
                  </Typography>
                  
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Group</TableCell>
                          <TableCell align="right">N</TableCell>
                          <TableCell align="right">Median</TableCell>
                          <TableCell align="right">Mean</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {testResults.groups && testResults.groups.map((group: { name: string; n: number; median: number; mean: number }, index: number) => (
                          <TableRow key={index}>
                            <TableCell>{group.name}</TableCell>
                            <TableCell align="right">{group.n}</TableCell>
                            <TableCell align="right">{group.median.toFixed(4)}</TableCell>
                            <TableCell align="right">{group.mean.toFixed(4)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  
                  {testResults.chartData && testResults.chartData.length > 0 && (
                    <Box height={200} mt={2}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={testResults.chartData}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 10,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <RechartsTooltip />
                          <Bar 
                            dataKey="value" 
                            fill={theme.palette.primary.main} 
                            name="Median"
                          >
                            {testResults.chartData.map((_: any, index: number) => (
                              <Cell key={`cell-${index}`} fill={`${theme.palette.primary.main}${index % 2 ? 'CC' : 'FF'}`} />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  )}
                </>
              )}
            </Grid>
            
            <Grid item xs={12}>
              <Paper elevation={0} variant="outlined" sx={{
                p: 2,
                bgcolor: theme.palette.mode === 'dark'
                  ? alpha(theme.palette.primary.main, 0.08)
                  : alpha(theme.palette.primary.main, 0.03),
                borderLeft: `4px solid ${theme.palette.primary.main}`,
                color: theme.palette.text.primary
              }}>
                <Typography variant="subtitle2" gutterBottom>
                  Interpretation
                </Typography>
                <Typography variant="body2" sx={{ color: theme.palette.text.primary }}>
                  {testResults.interpretation}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Paper>
      )}
      {/* Friedman Test Results Display */}
      {testResults && !loading && testType === 'friedman' && (
        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <InfoIcon sx={{ mr: 1, color: theme.palette.info.main }} />
            Friedman Test Results
          </Typography>
          <Typography variant="body1" gutterBottom>
            <strong>Interpretation:</strong> {(testResults as FriedmanTestResult).interpretation}
          </Typography>
          <TableContainer component={Paper} sx={{ mt: 2, mb: 2 }}>
            <Table size="small">
              <TableBody>
                <TableRow>
                  <TableCell><strong>Statistic (Chi-squared)</strong></TableCell>
                  <TableCell>{(testResults as FriedmanTestResult).statistic?.toFixed(3)}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><strong>Degrees of Freedom (df)</strong></TableCell>
                  <TableCell>{(testResults as FriedmanTestResult).df}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><strong>P-value</strong></TableCell>
                  <TableCell sx={{ color: (testResults as FriedmanTestResult).pValue < 0.05 ? 'success.main' : 'inherit', fontWeight: (testResults as FriedmanTestResult).pValue < 0.05 ? 'bold' : 'normal' }}>
                    {(testResults as FriedmanTestResult).pValue < 0.001 ? '< 0.001' : (testResults as FriedmanTestResult).pValue?.toFixed(5)}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><strong>Number of Subjects (N)</strong></TableCell>
                  <TableCell>{(testResults as FriedmanTestResult).n}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><strong>Number of Conditions (k)</strong></TableCell>
                  <TableCell>{(testResults as FriedmanTestResult).k}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>

          {(testResults as FriedmanTestResult).meanRanks && (testResults as FriedmanTestResult).meanRanks!.length > 0 && currentDataset && friedmanVariables.length > 0 && (
            <>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Mean Ranks for Variables:
              </Typography>
              <TableContainer component={Paper} sx={{ mb: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell sx={getHeaderCellSx()}>Variable</TableCell>
                      <TableCell align="right" sx={getHeaderCellSx()}>Mean Rank</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {friedmanVariables.map((varId, index) => {
                      if ((testResults as FriedmanTestResult).meanRanks && index < (testResults as FriedmanTestResult).meanRanks!.length) {
                        const columnName = currentDataset.columns.find(c => c.id === varId)?.name || `Variable ${index + 1}`;
                        return (
                          <TableRow key={varId}>
                            <TableCell>{columnName}</TableCell>
                            <TableCell align="right">{(testResults as FriedmanTestResult).meanRanks![index]?.toFixed(2)}</TableCell>
                          </TableRow>
                        );
                      }
                      return null;
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}

          <Paper elevation={0} variant="outlined" sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark'
              ? alpha(theme.palette.primary.main, 0.08)
              : alpha(theme.palette.primary.main, 0.03),
            borderLeft: `4px solid ${theme.palette.primary.main}`,
            color: theme.palette.text.primary
          }}>
            <Typography variant="subtitle2" gutterBottom>
              Interpretation
            </Typography>
            <Typography variant="body2" sx={{ color: theme.palette.text.primary }}>
              {getInterpretation(testResults)}
            </Typography>
          </Paper>
        </Paper>
      )}
    </Box>
  );
};

export default NonParametricTests;
