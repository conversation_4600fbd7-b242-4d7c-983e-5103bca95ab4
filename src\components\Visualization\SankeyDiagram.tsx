import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  SelectChangeEvent,
  TextField,
  FormGroup,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  useTheme,
  alpha,
  IconButton,
  Tooltip,
  Alert,
  AlertTitle,
  Chip,
  Stack
} from '@mui/material';
import {
  AccountTree as SankeyIcon,
  Tune as TuneIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  Add as AddIcon,
  Remove as RemoveIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, Column } from '../../types';
import * as Plotly from 'plotly.js';

// Plotly types for Sankey diagrams
type PlotlyData = Partial<Plotly.PlotData> & {
  type: 'sankey';
  node?: {
    pad?: number;
    thickness?: number;
    line?: {
      color?: string;
      width?: number;
    };
    label?: string[];
    color?: string[];
    x?: number[];
    y?: number[];
  };
  link?: {
    source?: number[];
    target?: number[];
    value?: number[];
    color?: string[];
    label?: string[];
  };
};

type PlotlyLayout = Partial<Plotly.Layout>;
type PlotlyConfig = Partial<Plotly.Config>;

// Color schemes for Sankey diagrams
const colorSchemes = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
  muted: ['#6b7280', '#9ca3af', '#d1d5db', '#374151', '#4b5563', '#6b7280', '#9ca3af', '#d1d5db'],
  categorical: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5'],
  viridis: ['#440154', '#482777', '#3f4a8a', '#31678e', '#26838f', '#1f9d8a', '#6cce5a', '#b6de2b', '#fee825'],
  plasma: ['#0d0887', '#5302a3', '#8b0aa5', '#b83289', '#db5c68', '#f48849', '#febd2a', '#f0f921']
};

type ColorSchemeName = keyof typeof colorSchemes;

// Chart Settings Interface
interface ChartSettings {
  title: string;
  nodeThickness: number;
  nodePadding: number;
  linkOpacity: number;
  colorScheme: ColorSchemeName;
  showValues: boolean;
  fontSize: number;
  arrangement: 'snap' | 'perpendicular' | 'freeform' | 'fixed';
}

// Default settings
const defaultChartSettings: ChartSettings = {
  title: 'Sankey Diagram',
  nodeThickness: 20,
  nodePadding: 10,
  linkOpacity: 0.6,
  colorScheme: 'muted',
  showValues: true,
  fontSize: 12,
  arrangement: 'snap'
};

const PLOTLY_SANKEY_DIV_ID = 'plotlySankeyDiv';

const SankeyDiagram: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const plotlyDivRef = useRef<HTMLDivElement>(null);
  
  // State
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [sourceVariable, setSourceVariable] = useState<string>('');
  const [intermediateVariables, setIntermediateVariables] = useState<string[]>([]); // Start with no intermediates
  const [targetVariable, setTargetVariable] = useState<string>('');
  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [plotlyConfig, setPlotlyConfig] = useState<{ data: PlotlyData[], layout: PlotlyLayout } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeDataset = React.useMemo(() => {
    if (!selectedDatasetId) return null;
    return datasets.find(ds => ds.id === selectedDatasetId) || null;
  }, [datasets, selectedDatasetId]);
  
  // Memoized columns - only categorical columns are suitable for Sankey diagrams
  const categoricalColumns = React.useMemo(() => 
    activeDataset?.columns.filter(col => col.type === DataType.CATEGORICAL) || [],
    [activeDataset]
  );
  
  // Effect to update selected dataset ID
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      setSourceVariable('');
      setIntermediateVariables([]);
      setTargetVariable('');
      setPlotlyConfig(null);
      setError(null);
    }
  }, [currentDataset]);

  // Effect to render Plotly chart
  useEffect(() => {
    if (plotlyConfig && plotlyDivRef.current) {
      const config: PlotlyConfig = { responsive: true };
      Plotly.newPlot(PLOTLY_SANKEY_DIV_ID, plotlyConfig.data, plotlyConfig.layout, config);
    }
    return () => {
      if (plotlyDivRef.current && typeof Plotly !== 'undefined' && Plotly.purge) {
        Plotly.purge(plotlyDivRef.current);
      }
    };
  }, [plotlyConfig]);

  // --- Handlers ---
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    setSelectedDatasetId(event.target.value);
    setSourceVariable('');
    setIntermediateVariables([]);
    setTargetVariable('');
    setPlotlyConfig(null);
    setError(null);
  };

  const handleSourceVariableChange = (event: SelectChangeEvent<string>) => {
    setSourceVariable(event.target.value);
    setPlotlyConfig(null);
    setError(null);
  };

  const handleIntermediateVariableChange = (index: number, value: string) => {
    const newIntermediates = [...intermediateVariables];
    newIntermediates[index] = value;
    setIntermediateVariables(newIntermediates);
    setPlotlyConfig(null);
    setError(null);
  };

  const handleAddIntermediateVariable = () => {
    if (intermediateVariables.length < 5) { // Limit to 5 intermediate variables
      setIntermediateVariables([...intermediateVariables, '']);
    }
  };

  const handleRemoveIntermediateVariable = (index: number) => {
    const newIntermediates = intermediateVariables.filter((_, i) => i !== index);
    setIntermediateVariables(newIntermediates);
    setPlotlyConfig(null);
    setError(null);
  };

  const handleTargetVariableChange = (event: SelectChangeEvent<string>) => {
    setTargetVariable(event.target.value);
    setPlotlyConfig(null);
    setError(null);
  };

  const handleSettingsChange = (key: keyof ChartSettings, value: any) => {
    setChartSettings(prev => ({ ...prev, [key]: value }));
  };

  // Validate data for Sankey diagram
  const validateData = (): string | null => {
    if (!activeDataset) return 'Please select a dataset.';
    if (!sourceVariable) return 'Please select a source variable.';
    if (!targetVariable) return 'Please select a target variable.';

    // Get all selected variables (source + intermediates + target)
    const allVariables = [sourceVariable, ...intermediateVariables.filter(v => v), targetVariable];
    const uniqueVariables = new Set(allVariables);

    if (uniqueVariables.size !== allVariables.length) {
      return 'All selected variables must be different.';
    }

    // Get valid intermediate variables (optional)
    const validIntermediates = intermediateVariables.filter(v => v);

    // Find all column objects
    const allColumns = allVariables.map(varId =>
      categoricalColumns.find(col => col.id === varId)
    );

    if (allColumns.some(col => !col)) {
      return 'One or more selected variables not found.';
    }

    // Check if we have enough data across all variables
    const validRows = activeDataset.data.filter(row =>
      allColumns.every(col =>
        col && row[col.name] != null && row[col.name] !== ''
      )
    );

    if (validRows.length < 2) {
      return 'Insufficient data. Need at least 2 valid rows across all selected variables.';
    }

    // Check if we have enough unique categories across all variables
    const totalUniqueCategories = allColumns.reduce((total, col) => {
      if (!col) return total;
      const categories = new Set(validRows.map(row => String(row[col.name])));
      return total + categories.size;
    }, 0);

    if (totalUniqueCategories < Math.max(2, allVariables.length)) {
      return 'Need more unique categories across all variables for meaningful flow visualization.';
    }

    return null;
  };

  // --- Data Generation for Plotly ---
  const generatePlotlyData = () => {
    const validationError = validateData();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError(null);
    setPlotlyConfig(null);

    try {
      // Get all selected variables in order
      const validIntermediates = intermediateVariables.filter(v => v);
      const allVariableIds = [sourceVariable, ...validIntermediates, targetVariable];

      // Find column objects for all variables
      const allColumns = allVariableIds.map(varId =>
        categoricalColumns.find(col => col.id === varId)!
      );

      // Filter valid rows (must have data for all selected variables)
      const validRows = activeDataset!.data.filter(row =>
        allColumns.every(col =>
          row[col.name] != null && row[col.name] !== ''
        )
      );

      // Create multi-stage flow data
      const flowMap = new Map<string, number>();
      const allNodes = new Set<string>();

      // Process each row to create flows between consecutive stages
      validRows.forEach(row => {
        // If no intermediate variables, create direct flow from source to target
        if (allColumns.length === 2) {
          const sourceCol = allColumns[0];
          const targetCol = allColumns[1];

          const sourceValue = String(row[sourceCol.name]);
          const targetValue = String(row[targetCol.name]);

          const sourceNode = `0_${sourceValue}`;
          const targetNode = `1_${targetValue}`;

          allNodes.add(sourceNode);
          allNodes.add(targetNode);

          const flowKey = `${sourceNode}→${targetNode}`;
          flowMap.set(flowKey, (flowMap.get(flowKey) || 0) + 1);
        } else {
          // Multi-stage flow with intermediate variables
          for (let i = 0; i < allColumns.length - 1; i++) {
            const sourceCol = allColumns[i];
            const targetCol = allColumns[i + 1];

            const sourceValue = String(row[sourceCol.name]);
            const targetValue = String(row[targetCol.name]);

            // Create unique node names with stage prefix to avoid conflicts
            const sourceNode = `${i}_${sourceValue}`;
            const targetNode = `${i + 1}_${targetValue}`;

            allNodes.add(sourceNode);
            allNodes.add(targetNode);

            const flowKey = `${sourceNode}→${targetNode}`;
            flowMap.set(flowKey, (flowMap.get(flowKey) || 0) + 1);
          }
        }
      });

      // Convert nodes to array and create mapping
      const nodeArray = Array.from(allNodes).sort((a, b) => {
        const [stageA, nameA] = a.split('_', 2);
        const [stageB, nameB] = b.split('_', 2);
        const stageNumA = parseInt(stageA);
        const stageNumB = parseInt(stageB);

        if (stageNumA !== stageNumB) {
          return stageNumA - stageNumB;
        }
        return nameA.localeCompare(nameB);
      });

      const nodeMap = new Map<string, number>();
      nodeArray.forEach((node, index) => {
        nodeMap.set(node, index);
      });

      // Prepare Sankey data
      const sources: number[] = [];
      const targets: number[] = [];
      const values: number[] = [];
      const linkLabels: string[] = [];

      flowMap.forEach((value, flowKey) => {
        const [sourceNode, targetNode] = flowKey.split('→');
        const sourceIndex = nodeMap.get(sourceNode)!;
        const targetIndex = nodeMap.get(targetNode)!;

        sources.push(sourceIndex);
        targets.push(targetIndex);
        values.push(value);

        // Create readable labels
        const sourceName = sourceNode.split('_', 2)[1];
        const targetName = targetNode.split('_', 2)[1];
        linkLabels.push(`${sourceName} → ${targetName}: ${value}`);
      });

      // Generate colors and positioning
      const colors = colorSchemes[chartSettings.colorScheme];
      const nodeColors: string[] = [];
      const nodeLabels: string[] = [];
      const nodeX: number[] = [];
      const nodeY: number[] = [];

      // Calculate positions and colors for nodes
      const stageCount = allColumns.length;
      nodeArray.forEach((node, index) => {
        const [stageStr, name] = node.split('_', 2);
        const stage = parseInt(stageStr);

        // Position nodes in columns based on stage
        const x = stage / (stageCount - 1);
        nodeX.push(x);

        // Distribute nodes vertically within each stage
        const nodesInStage = nodeArray.filter(n => n.startsWith(`${stage}_`));
        const positionInStage = nodesInStage.indexOf(node);
        const y = nodesInStage.length > 1 ? positionInStage / (nodesInStage.length - 1) : 0.5;
        nodeY.push(y);

        // Color based on stage
        nodeColors.push(colors[stage % colors.length]);
        nodeLabels.push(name);
      });

      const linkColors = sources.map(sourceIndex => {
        const sourceNode = nodeArray[sourceIndex];
        const stage = parseInt(sourceNode.split('_', 2)[0]);
        return colors[stage % colors.length] + Math.round(chartSettings.linkOpacity * 255).toString(16).padStart(2, '0');
      });

      const plotData: PlotlyData[] = [{
        type: 'sankey',
        node: {
          pad: chartSettings.nodePadding,
          thickness: chartSettings.nodeThickness,
          line: {
            color: theme.palette.divider,
            width: 1
          },
          label: nodeLabels,
          color: nodeColors,
          x: nodeX,
          y: nodeY
        },
        link: {
          source: sources,
          target: targets,
          value: values,
          color: linkColors,
          label: chartSettings.showValues ? linkLabels : undefined
        }
      }];

      const layout: PlotlyLayout = {
        title: {
          text: chartSettings.title,
          font: { size: chartSettings.fontSize + 4 }
        },
        font: { size: chartSettings.fontSize },
        paper_bgcolor: 'transparent',
        plot_bgcolor: 'transparent',
        margin: { l: 50, r: 50, t: 80, b: 50 }
      };

      setPlotlyConfig({ data: plotData, layout });
    } catch (err) {
      console.error('Sankey generation error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while generating the Sankey diagram');
    } finally {
      setLoading(false);
    }
  };

  // Download chart
  const downloadChart = () => {
    if (plotlyDivRef.current && plotlyConfig) {
      const downloadOpts: Plotly.DownloadImgopts = {
        format: 'svg',
        filename: chartSettings.title.replace(/\s+/g, '_') || 'sankey_diagram',
        width: plotlyDivRef.current.offsetWidth || 800,
        height: plotlyDivRef.current.offsetHeight || 600,
      };
      Plotly.downloadImage(PLOTLY_SANKEY_DIV_ID, downloadOpts);
    } else {
      setError('Chart data not available for download.');
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      {/* Information Section */}
      <Paper elevation={0} variant="outlined" sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
          <InfoIcon color="primary" sx={{ mt: 0.5, fontSize: 20 }} />
          <Box>
            <Typography variant="subtitle2" color="primary" gutterBottom>
              About Multi-Level Sankey Diagrams
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Sankey diagrams visualize flow relationships between categorical variables.
              Select a source and target variable to create basic flow diagrams, or add intermediate variables
              for multi-stage pathway visualizations. The width of each flow represents the frequency of cases
              moving through each stage. Perfect for analyzing career progressions, customer journeys,
              academic pathways, and other multi-step processes.
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Main Content */}
      <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
        {/* Controls Section */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          {/* Dataset Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth size="small">
              <InputLabel>Dataset</InputLabel>
              <Select
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
              >
                {datasets.map((dataset) => (
                  <MenuItem key={dataset.id} value={dataset.id}>
                    {dataset.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Source Variable */}
          <Grid item xs={12} md={6}>
            <FormControl fullWidth size="small" disabled={!activeDataset}>
              <InputLabel>Source Variable (Start)</InputLabel>
              <Select
                value={sourceVariable}
                label="Source Variable (Start)"
                onChange={handleSourceVariableChange}
              >
                {categoricalColumns.map((column) => (
                  <MenuItem key={column.id} value={column.id}>
                    {column.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Target Variable */}
          <Grid item xs={12} md={6}>
            <FormControl fullWidth size="small" disabled={!activeDataset}>
              <InputLabel>Target Variable (End)</InputLabel>
              <Select
                value={targetVariable}
                label="Target Variable (End)"
                onChange={handleTargetVariableChange}
              >
                {categoricalColumns.map((column) => (
                  <MenuItem key={column.id} value={column.id}>
                    {column.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Intermediate Variables Section */}
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle2" color="text.primary">
              Intermediate Variables (Pathway Steps) - Optional
            </Typography>
            <Box>
              <Tooltip title="Add intermediate variable">
                <span>
                  <IconButton
                    onClick={handleAddIntermediateVariable}
                    disabled={!activeDataset || intermediateVariables.length >= 5}
                    size="small"
                    color="primary"
                  >
                    <AddIcon />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          </Box>

          {intermediateVariables.length > 0 ? (
            <Stack spacing={1}>
              {intermediateVariables.map((variable, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip
                    label={`Step ${index + 1}`}
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{ minWidth: 70 }}
                  />
                  <FormControl fullWidth size="small" disabled={!activeDataset}>
                    <InputLabel>Select intermediate variable</InputLabel>
                    <Select
                      value={variable}
                      label="Select intermediate variable"
                      onChange={(e) => handleIntermediateVariableChange(index, e.target.value)}
                    >
                      {categoricalColumns.map((column) => (
                        <MenuItem key={column.id} value={column.id}>
                          {column.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <Tooltip title="Remove this intermediate variable">
                    <span>
                      <IconButton
                        onClick={() => handleRemoveIntermediateVariable(index)}
                        size="small"
                        color="error"
                      >
                        <RemoveIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                </Box>
              ))}
            </Stack>
          ) : (
            <Box sx={{
              p: 2,
              border: '1px dashed',
              borderColor: 'divider',
              borderRadius: 1,
              textAlign: 'center',
              bgcolor: 'grey.50'
            }}>
              <Typography variant="body2" color="text.secondary">
                No intermediate variables added. Click the + button to add pathway steps, or generate a direct flow diagram.
              </Typography>
            </Box>
          )}

          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {intermediateVariables.length > 0
              ? 'Add more intermediate variables to create multi-stage flow paths. Each step will show transitions from the previous stage.'
              : 'Intermediate variables are optional. Without them, you\'ll get a direct flow from source to target variable.'
            }
          </Typography>
        </Box>

        {/* Warning for insufficient categorical variables */}
        {activeDataset && categoricalColumns.length < 2 && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            <AlertTitle>Insufficient Categorical Variables</AlertTitle>
            This dataset has only {categoricalColumns.length} categorical variable(s).
            Sankey diagrams require at least 2 categorical variables (source + target).
            Add intermediate variables for multi-level flow visualization.
          </Alert>
        )}

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2, alignItems: 'center' }}>
          <Button
            variant="contained"
            onClick={generatePlotlyData}
            disabled={
              !sourceVariable ||
              !targetVariable ||
              loading
            }
            startIcon={loading ? <CircularProgress size={16} /> : <SankeyIcon />}
          >
            {loading ? 'Generating...' :
             intermediateVariables.filter(v => v).length > 0 ? 'Generate Multi-Level Sankey' : 'Generate Sankey Diagram'}
          </Button>

          <Tooltip title="Chart Settings">
            <IconButton
              onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
              color={showAdvancedSettings ? "primary" : "default"}
              size="small"
            >
              <TuneIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Download SVG">
            <span>
              <IconButton
                onClick={downloadChart}
                disabled={!plotlyConfig}
                size="small"
              >
                <SaveIcon />
              </IconButton>
            </span>
          </Tooltip>
        </Box>

        {/* Advanced Settings */}
        {showAdvancedSettings && (
          <Paper elevation={0} variant="outlined" sx={{
            p: 2,
            mb: 2,
            bgcolor: theme.palette.mode === 'dark'
              ? alpha(theme.palette.primary.main, 0.08)
              : alpha(theme.palette.primary.main, 0.03)
          }}>
            <Typography variant="subtitle2" gutterBottom>Chart Settings</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Chart Title"
                  value={chartSettings.title}
                  onChange={(e) => handleSettingsChange('title', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Color Scheme</InputLabel>
                  <Select
                    value={chartSettings.colorScheme}
                    label="Color Scheme"
                    onChange={(e) => handleSettingsChange('colorScheme', e.target.value)}
                  >
                    <MenuItem value="muted">Muted</MenuItem>
                    <MenuItem value="default">Default</MenuItem>
                    <MenuItem value="categorical">Categorical</MenuItem>
                    <MenuItem value="viridis">Viridis</MenuItem>
                    <MenuItem value="plasma">Plasma</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Node Thickness"
                  type="number"
                  value={chartSettings.nodeThickness}
                  onChange={(e) => handleSettingsChange('nodeThickness', Number(e.target.value))}
                  size="small"
                  inputProps={{ min: 5, max: 50 }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Node Padding"
                  type="number"
                  value={chartSettings.nodePadding}
                  onChange={(e) => handleSettingsChange('nodePadding', Number(e.target.value))}
                  size="small"
                  inputProps={{ min: 5, max: 50 }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Link Opacity"
                  type="number"
                  value={chartSettings.linkOpacity}
                  onChange={(e) => handleSettingsChange('linkOpacity', Number(e.target.value))}
                  size="small"
                  inputProps={{ min: 0.1, max: 1, step: 0.1 }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Font Size"
                  type="number"
                  value={chartSettings.fontSize}
                  onChange={(e) => handleSettingsChange('fontSize', Number(e.target.value))}
                  size="small"
                  inputProps={{ min: 8, max: 24 }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={chartSettings.showValues}
                        onChange={(e) => handleSettingsChange('showValues', e.target.checked)}
                      />
                    }
                    label="Show flow values on hover"
                  />
                </FormGroup>
              </Grid>
            </Grid>
          </Paper>
        )}

        {/* Chart Display */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6">{chartSettings.title}</Typography>
          <Box>
            <Tooltip title="Refresh Chart">
              <IconButton
                onClick={generatePlotlyData}
                size="small"
                disabled={
                  !sourceVariable ||
                  !targetVariable
                }
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Plotly Chart Div */}
        <Box
          ref={plotlyDivRef}
          id={PLOTLY_SANKEY_DIV_ID}
          sx={{
            height: 500,
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
            bgcolor: 'background.paper'
          }}
        >
          {loading && <CircularProgress />}
          {!loading && !plotlyConfig && !error && (
            <Typography variant="body1" color="text.secondary">
              Select source and target variables, then generate the diagram. Add intermediate variables for multi-level flows.
            </Typography>
          )}
          {error && (
            <Alert severity="error" sx={{ m: 2, maxWidth: '80%' }}>
              <AlertTitle>Error</AlertTitle>
              {error}
            </Alert>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default SankeyDiagram;
